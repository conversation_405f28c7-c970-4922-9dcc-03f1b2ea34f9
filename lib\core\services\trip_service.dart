import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/trip_model.dart';
import '../models/booking_model.dart';
import '../constants/app_constants.dart';
import '../config/supabase_config.dart';

class TripService {
  final SupabaseClient _supabase = SupabaseConfig.client;

  // Create new trip
  Future<String> createTrip(TripModel trip) async {
    try {
      final response = await _supabase
          .from(AppConstants.tripsCollection)
          .insert(trip.toMap())
          .select()
          .single();

      return response['id'];
    } catch (e) {
      throw 'خطأ في إنشاء الرحلة: ${e.toString()}';
    }
  }

  // Get trip by ID
  Future<TripModel?> getTripById(String tripId) async {
    try {
      final response = await _supabase
          .from(AppConstants.tripsCollection)
          .select()
          .eq('id', tripId)
          .single();

      return TripModel.fromMap(response);
    } catch (e) {
      throw 'خطأ في جلب بيانات الرحلة: ${e.toString()}';
    }
  }

  // Get trip stream (simplified for now)
  Stream<TripModel?> getTripStream(String tripId) {
    // Note: Supabase real-time subscriptions would be implemented here
    return Stream.fromFuture(getTripById(tripId));
  }

  // Update trip
  Future<void> updateTrip(String tripId, Map<String, dynamic> data) async {
    try {
      data['updated_at'] = DateTime.now().toIso8601String();
      await _supabase
          .from(AppConstants.tripsCollection)
          .update(data)
          .eq('id', tripId);
    } catch (e) {
      throw 'خطأ في تحديث الرحلة: ${e.toString()}';
    }
  }

  // Delete trip
  Future<void> deleteTrip(String tripId) async {
    try {
      await _supabase
          .from(AppConstants.tripsCollection)
          .delete()
          .eq('id', tripId);
    } catch (e) {
      throw 'خطأ في حذف الرحلة: ${e.toString()}';
    }
  }

  // Upload trip images
  Future<List<String>> uploadTripImages(String tripId, List<File> imageFiles) async {
    try {
      final List<String> urls = [];

      for (int i = 0; i < imageFiles.length; i++) {
        final String fileName = 'trip_${tripId}_$i.jpg';
        final String filePath = '${AppConstants.tripImagesPath}/$fileName';

        await _supabase.storage
            .from('images')
            .upload(filePath, imageFiles[i]);

        final String downloadUrl = _supabase.storage
            .from('images')
            .getPublicUrl(filePath);
        urls.add(downloadUrl);
      }

      return urls;
    } catch (e) {
      throw 'خطأ في رفع صور الرحلة: ${e.toString()}';
    }
  }

  // Get trips with basic filters
  Future<List<TripModel>> getTrips({
    String? fromCity,
    String? toDestination,
    String? leaderId,
    int limit = 20,
  }) async {
    try {
      var query = _supabase
          .from(AppConstants.tripsCollection)
          .select()
          .eq('is_active', true)
          .eq('status', AppConstants.tripStatusActive);

      // Apply basic filters
      if (fromCity != null) {
        query = query.eq('from_city', fromCity);
      }

      if (toDestination != null) {
        query = query.eq('to_destination', toDestination);
      }

      if (leaderId != null) {
        query = query.eq('leader_id', leaderId);
      }

      final response = await query
          .order('departure_date')
          .limit(limit);

      return (response as List)
          .map((data) => TripModel.fromMap(data))
          .toList();
    } catch (e) {
      throw 'خطأ في جلب الرحلات: ${e.toString()}';
    }
  }

  // Search trips
  Future<List<TripModel>> searchTrips({
    required String query,
    String? fromCity,
    String? toDestination,
    int limit = 20,
  }) async {
    try {
      var queryBuilder = _supabase
          .from(AppConstants.tripsCollection)
          .select()
          .eq('is_active', true)
          .eq('status', AppConstants.tripStatusActive);

      if (fromCity != null) {
        queryBuilder = queryBuilder.eq('from_city', fromCity);
      }

      if (toDestination != null) {
        queryBuilder = queryBuilder.eq('to_destination', toDestination);
      }

      final response = await queryBuilder.limit(limit * 2);

      final List<TripModel> trips = (response as List)
          .map((data) => TripModel.fromMap(data))
          .where((trip) =>
              trip.title.toLowerCase().contains(query.toLowerCase()) ||
              trip.description.toLowerCase().contains(query.toLowerCase()) ||
              trip.fromCity.toLowerCase().contains(query.toLowerCase()) ||
              trip.toDestination.toLowerCase().contains(query.toLowerCase()))
          .take(limit)
          .toList();

      return trips;
    } catch (e) {
      throw 'خطأ في البحث عن الرحلات: ${e.toString()}';
    }
  }

  // Get popular destinations
  Future<List<String>> getPopularDestinations({int limit = 10}) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection(AppConstants.tripsCollection)
          .where('isActive', isEqualTo: true)
          .where('status', isEqualTo: AppConstants.tripStatusActive)
          .get();

      final Map<String, int> destinationCounts = {};

      for (var doc in snapshot.docs) {
        final TripModel trip = TripModel.fromDocument(doc);
        destinationCounts[trip.toDestination] = 
            (destinationCounts[trip.toDestination] ?? 0) + 1;
      }

      final List<MapEntry<String, int>> sortedDestinations = 
          destinationCounts.entries.toList()
            ..sort((a, b) => b.value.compareTo(a.value));

      return sortedDestinations
          .take(limit)
          .map((entry) => entry.key)
          .toList();
    } catch (e) {
      throw 'خطأ في جلب الوجهات الشائعة: ${e.toString()}';
    }
  }

  // Get upcoming trips for user
  Future<List<TripModel>> getUpcomingTripsForUser(String userId) async {
    try {
      // Get user's bookings
      final QuerySnapshot bookingsSnapshot = await _firestore
          .collection(AppConstants.bookingsCollection)
          .where('travelerId', isEqualTo: userId)
          .where('status', isEqualTo: AppConstants.bookingStatusAccepted)
          .get();

      final List<String> tripIds = bookingsSnapshot.docs
          .map((doc) => BookingModel.fromDocument(doc).tripId)
          .toList();

      if (tripIds.isEmpty) return [];

      // Get trips
      final List<TripModel> trips = [];
      for (String tripId in tripIds) {
        final TripModel? trip = await getTripById(tripId);
        if (trip != null && trip.departureDate.isAfter(DateTime.now())) {
          trips.add(trip);
        }
      }

      // Sort by departure date
      trips.sort((a, b) => a.departureDate.compareTo(b.departureDate));

      return trips;
    } catch (e) {
      throw 'خطأ في جلب الرحلات القادمة: ${e.toString()}';
    }
  }

  // Get trip history for user
  Future<List<TripModel>> getTripHistoryForUser(String userId) async {
    try {
      // Get user's bookings
      final QuerySnapshot bookingsSnapshot = await _firestore
          .collection(AppConstants.bookingsCollection)
          .where('travelerId', isEqualTo: userId)
          .where('status', isEqualTo: AppConstants.bookingStatusAccepted)
          .get();

      final List<String> tripIds = bookingsSnapshot.docs
          .map((doc) => BookingModel.fromDocument(doc).tripId)
          .toList();

      if (tripIds.isEmpty) return [];

      // Get trips
      final List<TripModel> trips = [];
      for (String tripId in tripIds) {
        final TripModel? trip = await getTripById(tripId);
        if (trip != null && trip.departureDate.isBefore(DateTime.now())) {
          trips.add(trip);
        }
      }

      // Sort by departure date (most recent first)
      trips.sort((a, b) => b.departureDate.compareTo(a.departureDate));

      return trips;
    } catch (e) {
      throw 'خطأ في جلب تاريخ الرحلات: ${e.toString()}';
    }
  }

  // Update trip rating
  Future<void> updateTripRating(String tripId, double newRating, int reviewCount) async {
    try {
      await updateTrip(tripId, {
        'rating': newRating,
        'reviewCount': reviewCount,
      });
    } catch (e) {
      throw 'خطأ في تحديث تقييم الرحلة: ${e.toString()}';
    }
  }

  // Update available seats
  Future<void> updateAvailableSeats(String tripId, int newAvailableSeats) async {
    try {
      await updateTrip(tripId, {'availableSeats': newAvailableSeats});
    } catch (e) {
      throw 'خطأ في تحديث المقاعد المتاحة: ${e.toString()}';
    }
  }

  // Cancel trip
  Future<void> cancelTrip(String tripId, String reason) async {
    try {
      await updateTrip(tripId, {
        'status': AppConstants.tripStatusCancelled,
        'cancellationReason': reason,
        'isActive': false,
      });
    } catch (e) {
      throw 'خطأ في إلغاء الرحلة: ${e.toString()}';
    }
  }

  // Complete trip
  Future<void> completeTrip(String tripId) async {
    try {
      await updateTrip(tripId, {
        'status': AppConstants.tripStatusCompleted,
        'isActive': false,
      });
    } catch (e) {
      throw 'خطأ في إكمال الرحلة: ${e.toString()}';
    }
  }

  // Get trips by leader
  Future<List<TripModel>> getTripsByLeader(String leaderId, {int limit = 20}) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection(AppConstants.tripsCollection)
          .where('leaderId', isEqualTo: leaderId)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => TripModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw 'خطأ في جلب رحلات القائد: ${e.toString()}';
    }
  }
}
