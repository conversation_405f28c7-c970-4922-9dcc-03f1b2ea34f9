import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../models/trip_model.dart';
import '../models/booking_model.dart';
import '../constants/app_constants.dart';

class TripService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Create new trip
  Future<String> createTrip(TripModel trip) async {
    try {
      final DocumentReference docRef = await _firestore
          .collection(AppConstants.tripsCollection)
          .add(trip.toMap());

      // Update trip with generated ID
      await docRef.update({'id': docRef.id});

      return docRef.id;
    } catch (e) {
      throw 'خطأ في إنشاء الرحلة: ${e.toString()}';
    }
  }

  // Get trip by ID
  Future<TripModel?> getTripById(String tripId) async {
    try {
      final DocumentSnapshot doc = await _firestore
          .collection(AppConstants.tripsCollection)
          .doc(tripId)
          .get();

      if (doc.exists) {
        return TripModel.fromDocument(doc);
      }
    } catch (e) {
      throw 'خطأ في جلب بيانات الرحلة: ${e.toString()}';
    }
    return null;
  }

  // Get trip stream
  Stream<TripModel?> getTripStream(String tripId) {
    return _firestore
        .collection(AppConstants.tripsCollection)
        .doc(tripId)
        .snapshots()
        .map((doc) => doc.exists ? TripModel.fromDocument(doc) : null);
  }

  // Update trip
  Future<void> updateTrip(String tripId, Map<String, dynamic> data) async {
    try {
      data['updatedAt'] = Timestamp.now();
      await _firestore
          .collection(AppConstants.tripsCollection)
          .doc(tripId)
          .update(data);
    } catch (e) {
      throw 'خطأ في تحديث الرحلة: ${e.toString()}';
    }
  }

  // Delete trip
  Future<void> deleteTrip(String tripId) async {
    try {
      await _firestore
          .collection(AppConstants.tripsCollection)
          .doc(tripId)
          .delete();
    } catch (e) {
      throw 'خطأ في حذف الرحلة: ${e.toString()}';
    }
  }

  // Upload trip images
  Future<List<String>> uploadTripImages(String tripId, List<File> imageFiles) async {
    try {
      final List<String> urls = [];

      for (int i = 0; i < imageFiles.length; i++) {
        final String fileName = 'trip_${tripId}_$i.jpg';
        final Reference ref = _storage
            .ref()
            .child(AppConstants.tripImagesPath)
            .child(fileName);

        final UploadTask uploadTask = ref.putFile(imageFiles[i]);
        final TaskSnapshot snapshot = await uploadTask;
        final String downloadUrl = await snapshot.ref.getDownloadURL();
        urls.add(downloadUrl);
      }

      return urls;
    } catch (e) {
      throw 'خطأ في رفع صور الرحلة: ${e.toString()}';
    }
  }

  // Get trips with filters
  Future<List<TripModel>> getTrips({
    String? fromCity,
    String? toDestination,
    DateTime? departureDate,
    double? maxPrice,
    int? minSeats,
    String? tripType,
    double? minRating,
    String? leaderId,
    int limit = 20,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.tripsCollection)
          .where('isActive', isEqualTo: true)
          .where('status', isEqualTo: AppConstants.tripStatusActive);

      // Apply filters
      if (fromCity != null) {
        query = query.where('fromCity', isEqualTo: fromCity);
      }

      if (toDestination != null) {
        query = query.where('toDestination', isEqualTo: toDestination);
      }

      if (departureDate != null) {
        final DateTime startOfDay = DateTime(departureDate.year, departureDate.month, departureDate.day);
        final DateTime endOfDay = startOfDay.add(const Duration(days: 1));
        query = query
            .where('departureDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
            .where('departureDate', isLessThan: Timestamp.fromDate(endOfDay));
      }

      if (maxPrice != null) {
        query = query.where('price', isLessThanOrEqualTo: maxPrice);
      }

      if (minSeats != null) {
        query = query.where('availableSeats', isGreaterThanOrEqualTo: minSeats);
      }

      if (tripType != null) {
        query = query.where('tripType', isEqualTo: tripType);
      }

      if (minRating != null) {
        query = query.where('rating', isGreaterThanOrEqualTo: minRating);
      }

      if (leaderId != null) {
        query = query.where('leaderId', isEqualTo: leaderId);
      }

      // Order by departure date
      query = query.orderBy('departureDate');

      // Pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final QuerySnapshot snapshot = await query.get();

      return snapshot.docs
          .map((doc) => TripModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw 'خطأ في جلب الرحلات: ${e.toString()}';
    }
  }

  // Search trips
  Future<List<TripModel>> searchTrips({
    required String query,
    String? fromCity,
    String? toDestination,
    int limit = 20,
  }) async {
    try {
      Query firestoreQuery = _firestore
          .collection(AppConstants.tripsCollection)
          .where('isActive', isEqualTo: true)
          .where('status', isEqualTo: AppConstants.tripStatusActive);

      if (fromCity != null) {
        firestoreQuery = firestoreQuery.where('fromCity', isEqualTo: fromCity);
      }

      if (toDestination != null) {
        firestoreQuery = firestoreQuery.where('toDestination', isEqualTo: toDestination);
      }

      final QuerySnapshot snapshot = await firestoreQuery
          .limit(limit * 2) // Get more to filter locally
          .get();

      final List<TripModel> trips = snapshot.docs
          .map((doc) => TripModel.fromDocument(doc))
          .where((trip) => 
              trip.title.toLowerCase().contains(query.toLowerCase()) ||
              trip.description.toLowerCase().contains(query.toLowerCase()) ||
              trip.fromCity.toLowerCase().contains(query.toLowerCase()) ||
              trip.toDestination.toLowerCase().contains(query.toLowerCase()))
          .take(limit)
          .toList();

      return trips;
    } catch (e) {
      throw 'خطأ في البحث عن الرحلات: ${e.toString()}';
    }
  }

  // Get popular destinations
  Future<List<String>> getPopularDestinations({int limit = 10}) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection(AppConstants.tripsCollection)
          .where('isActive', isEqualTo: true)
          .where('status', isEqualTo: AppConstants.tripStatusActive)
          .get();

      final Map<String, int> destinationCounts = {};

      for (var doc in snapshot.docs) {
        final TripModel trip = TripModel.fromDocument(doc);
        destinationCounts[trip.toDestination] = 
            (destinationCounts[trip.toDestination] ?? 0) + 1;
      }

      final List<MapEntry<String, int>> sortedDestinations = 
          destinationCounts.entries.toList()
            ..sort((a, b) => b.value.compareTo(a.value));

      return sortedDestinations
          .take(limit)
          .map((entry) => entry.key)
          .toList();
    } catch (e) {
      throw 'خطأ في جلب الوجهات الشائعة: ${e.toString()}';
    }
  }

  // Get upcoming trips for user
  Future<List<TripModel>> getUpcomingTripsForUser(String userId) async {
    try {
      // Get user's bookings
      final QuerySnapshot bookingsSnapshot = await _firestore
          .collection(AppConstants.bookingsCollection)
          .where('travelerId', isEqualTo: userId)
          .where('status', isEqualTo: AppConstants.bookingStatusAccepted)
          .get();

      final List<String> tripIds = bookingsSnapshot.docs
          .map((doc) => BookingModel.fromDocument(doc).tripId)
          .toList();

      if (tripIds.isEmpty) return [];

      // Get trips
      final List<TripModel> trips = [];
      for (String tripId in tripIds) {
        final TripModel? trip = await getTripById(tripId);
        if (trip != null && trip.departureDate.isAfter(DateTime.now())) {
          trips.add(trip);
        }
      }

      // Sort by departure date
      trips.sort((a, b) => a.departureDate.compareTo(b.departureDate));

      return trips;
    } catch (e) {
      throw 'خطأ في جلب الرحلات القادمة: ${e.toString()}';
    }
  }

  // Get trip history for user
  Future<List<TripModel>> getTripHistoryForUser(String userId) async {
    try {
      // Get user's bookings
      final QuerySnapshot bookingsSnapshot = await _firestore
          .collection(AppConstants.bookingsCollection)
          .where('travelerId', isEqualTo: userId)
          .where('status', isEqualTo: AppConstants.bookingStatusAccepted)
          .get();

      final List<String> tripIds = bookingsSnapshot.docs
          .map((doc) => BookingModel.fromDocument(doc).tripId)
          .toList();

      if (tripIds.isEmpty) return [];

      // Get trips
      final List<TripModel> trips = [];
      for (String tripId in tripIds) {
        final TripModel? trip = await getTripById(tripId);
        if (trip != null && trip.departureDate.isBefore(DateTime.now())) {
          trips.add(trip);
        }
      }

      // Sort by departure date (most recent first)
      trips.sort((a, b) => b.departureDate.compareTo(a.departureDate));

      return trips;
    } catch (e) {
      throw 'خطأ في جلب تاريخ الرحلات: ${e.toString()}';
    }
  }

  // Update trip rating
  Future<void> updateTripRating(String tripId, double newRating, int reviewCount) async {
    try {
      await updateTrip(tripId, {
        'rating': newRating,
        'reviewCount': reviewCount,
      });
    } catch (e) {
      throw 'خطأ في تحديث تقييم الرحلة: ${e.toString()}';
    }
  }

  // Update available seats
  Future<void> updateAvailableSeats(String tripId, int newAvailableSeats) async {
    try {
      await updateTrip(tripId, {'availableSeats': newAvailableSeats});
    } catch (e) {
      throw 'خطأ في تحديث المقاعد المتاحة: ${e.toString()}';
    }
  }

  // Cancel trip
  Future<void> cancelTrip(String tripId, String reason) async {
    try {
      await updateTrip(tripId, {
        'status': AppConstants.tripStatusCancelled,
        'cancellationReason': reason,
        'isActive': false,
      });
    } catch (e) {
      throw 'خطأ في إلغاء الرحلة: ${e.toString()}';
    }
  }

  // Complete trip
  Future<void> completeTrip(String tripId) async {
    try {
      await updateTrip(tripId, {
        'status': AppConstants.tripStatusCompleted,
        'isActive': false,
      });
    } catch (e) {
      throw 'خطأ في إكمال الرحلة: ${e.toString()}';
    }
  }

  // Get trips by leader
  Future<List<TripModel>> getTripsByLeader(String leaderId, {int limit = 20}) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection(AppConstants.tripsCollection)
          .where('leaderId', isEqualTo: leaderId)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => TripModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw 'خطأ في جلب رحلات القائد: ${e.toString()}';
    }
  }
}
