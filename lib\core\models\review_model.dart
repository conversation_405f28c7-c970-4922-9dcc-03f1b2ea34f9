import 'package:cloud_firestore/cloud_firestore.dart';

class ReviewModel {
  final String id;
  final String tripId; // معرف الرحلة
  final String bookingId; // معرف الحجز
  final String reviewerId; // معرف المراجع
  final String reviewerName; // اسم المراجع
  final String reviewerImageUrl; // صورة المراجع
  final String leaderId; // معرف قائد الرحلة
  final String leaderName; // اسم قائد الرحلة
  final String tripTitle; // عنوان الرحلة
  final double rating; // التقييم (1-5)
  final String comment; // التعليق
  final List<String> imageUrls; // صور المراجعة
  final DateTime createdAt; // تاريخ الإنشاء
  final DateTime updatedAt; // تاريخ التحديث
  final bool isVisible; // مرئي
  final bool isReported; // تم الإبلاغ عنه
  final int helpfulCount; // عدد الأشخاص الذين وجدوه مفيداً
  final List<String> helpfulUsers; // المستخدمون الذين وجدوه مفيداً
  final String? leaderResponse; // رد قائد الرحلة
  final DateTime? leaderResponseDate; // تاريخ رد قائد الرحلة

  ReviewModel({
    required this.id,
    required this.tripId,
    required this.bookingId,
    required this.reviewerId,
    required this.reviewerName,
    required this.reviewerImageUrl,
    required this.leaderId,
    required this.leaderName,
    required this.tripTitle,
    required this.rating,
    required this.comment,
    this.imageUrls = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isVisible = true,
    this.isReported = false,
    this.helpfulCount = 0,
    this.helpfulUsers = const [],
    this.leaderResponse,
    this.leaderResponseDate,
  });

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'tripId': tripId,
      'bookingId': bookingId,
      'reviewerId': reviewerId,
      'reviewerName': reviewerName,
      'reviewerImageUrl': reviewerImageUrl,
      'leaderId': leaderId,
      'leaderName': leaderName,
      'tripTitle': tripTitle,
      'rating': rating,
      'comment': comment,
      'imageUrls': imageUrls,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isVisible': isVisible,
      'isReported': isReported,
      'helpfulCount': helpfulCount,
      'helpfulUsers': helpfulUsers,
      'leaderResponse': leaderResponse,
      'leaderResponseDate': leaderResponseDate != null ? Timestamp.fromDate(leaderResponseDate!) : null,
    };
  }

  // Create from Firestore document
  factory ReviewModel.fromMap(Map<String, dynamic> map) {
    return ReviewModel(
      id: map['id'] ?? '',
      tripId: map['tripId'] ?? '',
      bookingId: map['bookingId'] ?? '',
      reviewerId: map['reviewerId'] ?? '',
      reviewerName: map['reviewerName'] ?? '',
      reviewerImageUrl: map['reviewerImageUrl'] ?? '',
      leaderId: map['leaderId'] ?? '',
      leaderName: map['leaderName'] ?? '',
      tripTitle: map['tripTitle'] ?? '',
      rating: (map['rating'] ?? 0.0).toDouble(),
      comment: map['comment'] ?? '',
      imageUrls: List<String>.from(map['imageUrls'] ?? []),
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isVisible: map['isVisible'] ?? true,
      isReported: map['isReported'] ?? false,
      helpfulCount: map['helpfulCount'] ?? 0,
      helpfulUsers: List<String>.from(map['helpfulUsers'] ?? []),
      leaderResponse: map['leaderResponse'],
      leaderResponseDate: (map['leaderResponseDate'] as Timestamp?)?.toDate(),
    );
  }

  // Create from Firestore DocumentSnapshot
  factory ReviewModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ReviewModel.fromMap({...data, 'id': doc.id});
  }

  // Copy with method for updates
  ReviewModel copyWith({
    double? rating,
    String? comment,
    List<String>? imageUrls,
    DateTime? updatedAt,
    bool? isVisible,
    bool? isReported,
    int? helpfulCount,
    List<String>? helpfulUsers,
    String? leaderResponse,
    DateTime? leaderResponseDate,
  }) {
    return ReviewModel(
      id: id,
      tripId: tripId,
      bookingId: bookingId,
      reviewerId: reviewerId,
      reviewerName: reviewerName,
      reviewerImageUrl: reviewerImageUrl,
      leaderId: leaderId,
      leaderName: leaderName,
      tripTitle: tripTitle,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      imageUrls: imageUrls ?? this.imageUrls,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      isVisible: isVisible ?? this.isVisible,
      isReported: isReported ?? this.isReported,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      helpfulUsers: helpfulUsers ?? this.helpfulUsers,
      leaderResponse: leaderResponse ?? this.leaderResponse,
      leaderResponseDate: leaderResponseDate ?? this.leaderResponseDate,
    );
  }

  // Get rating stars as string
  String get ratingStars {
    String stars = '';
    for (int i = 1; i <= 5; i++) {
      if (i <= rating) {
        stars += '★';
      } else {
        stars += '☆';
      }
    }
    return stars;
  }

  // Get rating description in Arabic
  String get ratingDescription {
    if (rating >= 4.5) return 'ممتاز';
    if (rating >= 4.0) return 'جيد جداً';
    if (rating >= 3.5) return 'جيد';
    if (rating >= 3.0) return 'مقبول';
    if (rating >= 2.0) return 'ضعيف';
    return 'سيء جداً';
  }

  // Check if user found this review helpful
  bool isHelpfulByUser(String userId) {
    return helpfulUsers.contains(userId);
  }

  // Check if leader has responded
  bool get hasLeaderResponse => leaderResponse != null && leaderResponse!.isNotEmpty;

  // Get formatted creation date
  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 30) {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    } else if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  // Check if review is recent (within 7 days)
  bool get isRecent => DateTime.now().difference(createdAt).inDays <= 7;

  // Check if review has images
  bool get hasImages => imageUrls.isNotEmpty;

  @override
  String toString() {
    return 'ReviewModel(id: $id, tripId: $tripId, rating: $rating, comment: $comment)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReviewModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class Timestamp {
  static fromDate(DateTime createdAt) {}
  
  toDate() {}
}
