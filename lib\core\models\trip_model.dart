import 'package:cloud_firestore/cloud_firestore.dart';

class TripModel {
  final String id;
  final String leaderId; // معرف قائد الرحلة
  final String leaderName; // اسم قائد الرحلة
  final String leaderImageUrl; // صورة قائد الرحلة
  final String title; // عنوان الرحلة
  final String description; // وصف الرحلة
  final String fromCity; // من مدينة
  final String toDestination; // إلى وجهة
  final DateTime departureDate; // تاريخ المغادرة
  final String departureTime; // وقت المغادرة
  final String meetingPoint; // نقطة اللقاء
  final double price; // السعر
  final int totalSeats; // إجمالي المقاعد
  final int availableSeats; // المقاعد المتاحة
  final List<String> imageUrls; // صور الرحلة
  final String vehicleType; // نوع المركبة
  final String vehicleImageUrl; // صورة المركبة
  final String tripType; // نوع الرحلة (general/family/women_only)
  final String status; // حالة الرحلة
  final List<String> rules; // قوانين الرحلة
  final List<TripProgram> program; // برنامج الرحلة
  final DateTime createdAt; // تاريخ الإنشاء
  final DateTime updatedAt; // تاريخ التحديث
  final double rating; // التقييم
  final int reviewCount; // عدد التقييمات
  final List<String> tags; // العلامات
  final bool isActive; // نشط
  final String? returnTime; // وقت العودة
  final double? estimatedDuration; // المدة المقدرة بالساعات
  final List<String> amenities; // المرافق المتاحة
  final bool allowsRefund; // يسمح بالاسترداد
  final DateTime? refundDeadline; // موعد نهاية الاسترداد

  TripModel({
    required this.id,
    required this.leaderId,
    required this.leaderName,
    required this.leaderImageUrl,
    required this.title,
    required this.description,
    required this.fromCity,
    required this.toDestination,
    required this.departureDate,
    required this.departureTime,
    required this.meetingPoint,
    required this.price,
    required this.totalSeats,
    required this.availableSeats,
    this.imageUrls = const [],
    required this.vehicleType,
    required this.vehicleImageUrl,
    this.tripType = 'general',
    this.status = 'active',
    this.rules = const [],
    this.program = const [],
    required this.createdAt,
    required this.updatedAt,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.tags = const [],
    this.isActive = true,
    this.returnTime,
    this.estimatedDuration,
    this.amenities = const [],
    this.allowsRefund = false,
    this.refundDeadline,
  });

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'leaderId': leaderId,
      'leaderName': leaderName,
      'leaderImageUrl': leaderImageUrl,
      'title': title,
      'description': description,
      'fromCity': fromCity,
      'toDestination': toDestination,
      'departureDate': Timestamp.fromDate(departureDate),
      'departureTime': departureTime,
      'meetingPoint': meetingPoint,
      'price': price,
      'totalSeats': totalSeats,
      'availableSeats': availableSeats,
      'imageUrls': imageUrls,
      'vehicleType': vehicleType,
      'vehicleImageUrl': vehicleImageUrl,
      'tripType': tripType,
      'status': status,
      'rules': rules,
      'program': program.map((p) => p.toMap()).toList(),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'rating': rating,
      'reviewCount': reviewCount,
      'tags': tags,
      'isActive': isActive,
      'returnTime': returnTime,
      'estimatedDuration': estimatedDuration,
      'amenities': amenities,
      'allowsRefund': allowsRefund,
      'refundDeadline': refundDeadline != null ? Timestamp.fromDate(refundDeadline!) : null,
    };
  }

  // Create from Firestore document
  factory TripModel.fromMap(Map<String, dynamic> map) {
    return TripModel(
      id: map['id'] ?? '',
      leaderId: map['leaderId'] ?? '',
      leaderName: map['leaderName'] ?? '',
      leaderImageUrl: map['leaderImageUrl'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      fromCity: map['fromCity'] ?? '',
      toDestination: map['toDestination'] ?? '',
      departureDate: (map['departureDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      departureTime: map['departureTime'] ?? '',
      meetingPoint: map['meetingPoint'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
      totalSeats: map['totalSeats'] ?? 0,
      availableSeats: map['availableSeats'] ?? 0,
      imageUrls: List<String>.from(map['imageUrls'] ?? []),
      vehicleType: map['vehicleType'] ?? '',
      vehicleImageUrl: map['vehicleImageUrl'] ?? '',
      tripType: map['tripType'] ?? 'general',
      status: map['status'] ?? 'active',
      rules: List<String>.from(map['rules'] ?? []),
      program: (map['program'] as List<dynamic>?)
          ?.map((p) => TripProgram.fromMap(p as Map<String, dynamic>))
          .toList() ?? [],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      rating: (map['rating'] ?? 0.0).toDouble(),
      reviewCount: map['reviewCount'] ?? 0,
      tags: List<String>.from(map['tags'] ?? []),
      isActive: map['isActive'] ?? true,
      returnTime: map['returnTime'],
      estimatedDuration: map['estimatedDuration']?.toDouble(),
      amenities: List<String>.from(map['amenities'] ?? []),
      allowsRefund: map['allowsRefund'] ?? false,
      refundDeadline: (map['refundDeadline'] as Timestamp?)?.toDate(),
    );
  }

  // Create from Firestore DocumentSnapshot
  factory TripModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return TripModel.fromMap({...data, 'id': doc.id});
  }

  // Copy with method for updates
  TripModel copyWith({
    String? title,
    String? description,
    String? fromCity,
    String? toDestination,
    DateTime? departureDate,
    String? departureTime,
    String? meetingPoint,
    double? price,
    int? totalSeats,
    int? availableSeats,
    List<String>? imageUrls,
    String? vehicleType,
    String? vehicleImageUrl,
    String? tripType,
    String? status,
    List<String>? rules,
    List<TripProgram>? program,
    DateTime? updatedAt,
    double? rating,
    int? reviewCount,
    List<String>? tags,
    bool? isActive,
    String? returnTime,
    double? estimatedDuration,
    List<String>? amenities,
    bool? allowsRefund,
    DateTime? refundDeadline,
  }) {
    return TripModel(
      id: id,
      leaderId: leaderId,
      leaderName: leaderName,
      leaderImageUrl: leaderImageUrl,
      title: title ?? this.title,
      description: description ?? this.description,
      fromCity: fromCity ?? this.fromCity,
      toDestination: toDestination ?? this.toDestination,
      departureDate: departureDate ?? this.departureDate,
      departureTime: departureTime ?? this.departureTime,
      meetingPoint: meetingPoint ?? this.meetingPoint,
      price: price ?? this.price,
      totalSeats: totalSeats ?? this.totalSeats,
      availableSeats: availableSeats ?? this.availableSeats,
      imageUrls: imageUrls ?? this.imageUrls,
      vehicleType: vehicleType ?? this.vehicleType,
      vehicleImageUrl: vehicleImageUrl ?? this.vehicleImageUrl,
      tripType: tripType ?? this.tripType,
      status: status ?? this.status,
      rules: rules ?? this.rules,
      program: program ?? this.program,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      tags: tags ?? this.tags,
      isActive: isActive ?? this.isActive,
      returnTime: returnTime ?? this.returnTime,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      amenities: amenities ?? this.amenities,
      allowsRefund: allowsRefund ?? this.allowsRefund,
      refundDeadline: refundDeadline ?? this.refundDeadline,
    );
  }

  // Check if trip is bookable
  bool get isBookable {
    return isActive && 
           status == 'active' && 
           availableSeats > 0 && 
           departureDate.isAfter(DateTime.now());
  }

  // Check if trip is full
  bool get isFull => availableSeats <= 0;

  // Get booked seats count
  int get bookedSeats => totalSeats - availableSeats;

  // Check if refund is allowed
  bool get canRefund {
    if (!allowsRefund) return false;
    if (refundDeadline == null) return true;
    return DateTime.now().isBefore(refundDeadline!);
  }

  @override
  String toString() {
    return 'TripModel(id: $id, title: $title, fromCity: $fromCity, toDestination: $toDestination)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TripModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class TripProgram {
  final String time; // الوقت
  final String activity; // النشاط
  final String? location; // الموقع
  final String? icon; // الأيقونة

  TripProgram({
    required this.time,
    required this.activity,
    this.location,
    this.icon,
  });

  Map<String, dynamic> toMap() {
    return {
      'time': time,
      'activity': activity,
      'location': location,
      'icon': icon,
    };
  }

  factory TripProgram.fromMap(Map<String, dynamic> map) {
    return TripProgram(
      time: map['time'] ?? '',
      activity: map['activity'] ?? '',
      location: map['location'],
      icon: map['icon'],
    );
  }

  @override
  String toString() {
    return 'TripProgram(time: $time, activity: $activity, location: $location)';
  }
}
