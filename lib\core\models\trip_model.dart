

class TripModel {
  final String id;
  final String leaderId; // معرف قائد الرحلة
  final String leaderName; // اسم قائد الرحلة
  final String leaderImageUrl; // صورة قائد الرحلة
  final String title; // عنوان الرحلة
  final String description; // وصف الرحلة
  final String fromCity; // من مدينة
  final String toDestination; // إلى وجهة
  final DateTime departureDate; // تاريخ المغادرة
  final String departureTime; // وقت المغادرة
  final String meetingPoint; // نقطة اللقاء
  final double price; // السعر
  final int totalSeats; // إجمالي المقاعد
  final int availableSeats; // المقاعد المتاحة
  final List<String> imageUrls; // صور الرحلة
  final String vehicleType; // نوع المركبة
  final String vehicleImageUrl; // صورة المركبة
  final String tripType; // نوع الرحلة (general/family/women_only)
  final String status; // حالة الرحلة
  final List<String> rules; // قوانين الرحلة
  final List<TripProgram> program; // برنامج الرحلة
  final DateTime createdAt; // تاريخ الإنشاء
  final DateTime updatedAt; // تاريخ التحديث
  final double rating; // التقييم
  final int reviewCount; // عدد التقييمات
  final List<String> tags; // العلامات
  final bool isActive; // نشط
  final String? returnTime; // وقت العودة
  final double? estimatedDuration; // المدة المقدرة بالساعات
  final List<String> amenities; // المرافق المتاحة
  final bool allowsRefund; // يسمح بالاسترداد
  final DateTime? refundDeadline; // موعد نهاية الاسترداد

  TripModel({
    required this.id,
    required this.leaderId,
    required this.leaderName,
    required this.leaderImageUrl,
    required this.title,
    required this.description,
    required this.fromCity,
    required this.toDestination,
    required this.departureDate,
    required this.departureTime,
    required this.meetingPoint,
    required this.price,
    required this.totalSeats,
    required this.availableSeats,
    this.imageUrls = const [],
    required this.vehicleType,
    required this.vehicleImageUrl,
    this.tripType = 'general',
    this.status = 'active',
    this.rules = const [],
    this.program = const [],
    required this.createdAt,
    required this.updatedAt,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.tags = const [],
    this.isActive = true,
    this.returnTime,
    this.estimatedDuration,
    this.amenities = const [],
    this.allowsRefund = false,
    this.refundDeadline,
  });

  // Convert to Map for Supabase
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'leader_id': leaderId,
      'leader_name': leaderName,
      'leader_image_url': leaderImageUrl,
      'title': title,
      'description': description,
      'from_city': fromCity,
      'to_destination': toDestination,
      'departure_date': departureDate.toIso8601String(),
      'departure_time': departureTime,
      'meeting_point': meetingPoint,
      'price': price,
      'total_seats': totalSeats,
      'available_seats': availableSeats,
      'image_urls': imageUrls,
      'vehicle_type': vehicleType,
      'vehicle_image_url': vehicleImageUrl,
      'trip_type': tripType,
      'status': status,
      'rules': rules,
      'program': program.map((p) => p.toMap()).toList(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'rating': rating,
      'review_count': reviewCount,
      'tags': tags,
      'is_active': isActive,
      'return_time': returnTime,
      'estimated_duration': estimatedDuration,
      'amenities': amenities,
      'allows_refund': allowsRefund,
      'refund_deadline': refundDeadline?.toIso8601String(),
    };
  }

  // Create from Supabase response
  factory TripModel.fromMap(Map<String, dynamic> map) {
    return TripModel(
      id: map['id'] ?? '',
      leaderId: map['leader_id'] ?? '',
      leaderName: map['leader_name'] ?? '',
      leaderImageUrl: map['leader_image_url'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      fromCity: map['from_city'] ?? '',
      toDestination: map['to_destination'] ?? '',
      departureDate: map['departure_date'] != null ? DateTime.parse(map['departure_date']) : DateTime.now(),
      departureTime: map['departure_time'] ?? '',
      meetingPoint: map['meeting_point'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
      totalSeats: map['total_seats'] ?? 0,
      availableSeats: map['available_seats'] ?? 0,
      imageUrls: List<String>.from(map['image_urls'] ?? []),
      vehicleType: map['vehicle_type'] ?? '',
      vehicleImageUrl: map['vehicle_image_url'] ?? '',
      tripType: map['trip_type'] ?? 'general',
      status: map['status'] ?? 'active',
      rules: List<String>.from(map['rules'] ?? []),
      program: (map['program'] as List<dynamic>?)
          ?.map((p) => TripProgram.fromMap(p as Map<String, dynamic>))
          .toList() ?? [],
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : DateTime.now(),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : DateTime.now(),
      rating: (map['rating'] ?? 0.0).toDouble(),
      reviewCount: map['review_count'] ?? 0,
      tags: List<String>.from(map['tags'] ?? []),
      isActive: map['is_active'] ?? true,
      returnTime: map['return_time'],
      estimatedDuration: map['estimated_duration']?.toDouble(),
      amenities: List<String>.from(map['amenities'] ?? []),
      allowsRefund: map['allows_refund'] ?? false,
      refundDeadline: map['refund_deadline'] != null ? DateTime.parse(map['refund_deadline']) : null,
    );
  }



  // Copy with method for updates
  TripModel copyWith({
    String? title,
    String? description,
    String? fromCity,
    String? toDestination,
    DateTime? departureDate,
    String? departureTime,
    String? meetingPoint,
    double? price,
    int? totalSeats,
    int? availableSeats,
    List<String>? imageUrls,
    String? vehicleType,
    String? vehicleImageUrl,
    String? tripType,
    String? status,
    List<String>? rules,
    List<TripProgram>? program,
    DateTime? updatedAt,
    double? rating,
    int? reviewCount,
    List<String>? tags,
    bool? isActive,
    String? returnTime,
    double? estimatedDuration,
    List<String>? amenities,
    bool? allowsRefund,
    DateTime? refundDeadline,
  }) {
    return TripModel(
      id: id,
      leaderId: leaderId,
      leaderName: leaderName,
      leaderImageUrl: leaderImageUrl,
      title: title ?? this.title,
      description: description ?? this.description,
      fromCity: fromCity ?? this.fromCity,
      toDestination: toDestination ?? this.toDestination,
      departureDate: departureDate ?? this.departureDate,
      departureTime: departureTime ?? this.departureTime,
      meetingPoint: meetingPoint ?? this.meetingPoint,
      price: price ?? this.price,
      totalSeats: totalSeats ?? this.totalSeats,
      availableSeats: availableSeats ?? this.availableSeats,
      imageUrls: imageUrls ?? this.imageUrls,
      vehicleType: vehicleType ?? this.vehicleType,
      vehicleImageUrl: vehicleImageUrl ?? this.vehicleImageUrl,
      tripType: tripType ?? this.tripType,
      status: status ?? this.status,
      rules: rules ?? this.rules,
      program: program ?? this.program,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      tags: tags ?? this.tags,
      isActive: isActive ?? this.isActive,
      returnTime: returnTime ?? this.returnTime,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      amenities: amenities ?? this.amenities,
      allowsRefund: allowsRefund ?? this.allowsRefund,
      refundDeadline: refundDeadline ?? this.refundDeadline,
    );
  }

  // Check if trip is bookable
  bool get isBookable {
    return isActive && 
           status == 'active' && 
           availableSeats > 0 && 
           departureDate.isAfter(DateTime.now());
  }

  // Check if trip is full
  bool get isFull => availableSeats <= 0;

  // Get booked seats count
  int get bookedSeats => totalSeats - availableSeats;

  // Check if refund is allowed
  bool get canRefund {
    if (!allowsRefund) return false;
    if (refundDeadline == null) return true;
    return DateTime.now().isBefore(refundDeadline!);
  }

  @override
  String toString() {
    return 'TripModel(id: $id, title: $title, fromCity: $fromCity, toDestination: $toDestination)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TripModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class TripProgram {
  final String time; // الوقت
  final String activity; // النشاط
  final String? location; // الموقع
  final String? icon; // الأيقونة

  TripProgram({
    required this.time,
    required this.activity,
    this.location,
    this.icon,
  });

  Map<String, dynamic> toMap() {
    return {
      'time': time,
      'activity': activity,
      'location': location,
      'icon': icon,
    };
  }

  factory TripProgram.fromMap(Map<String, dynamic> map) {
    return TripProgram(
      time: map['time'] ?? '',
      activity: map['activity'] ?? '',
      location: map['location'],
      icon: map['icon'],
    );
  }

  @override
  String toString() {
    return 'TripProgram(time: $time, activity: $activity, location: $location)';
  }
}
