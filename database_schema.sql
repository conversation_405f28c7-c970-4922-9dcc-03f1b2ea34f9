-- <PERSON><PERSON><PERSON> App Database Schema for Supabase
-- Run these SQL commands in your Supabase SQL editor

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT auth.uid(),
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    phone_number TEXT,
    user_type TEXT NOT NULL CHECK (user_type IN ('traveler', 'leader')),
    profile_image_url TEXT,
    city TEXT NOT NULL,
    bio TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    points INTEGER DEFAULT 0,
    badges TEXT[] DEFAULT '{}',
    rating DECIMAL(2,1) DEFAULT 0.0,
    total_trips INTEGER DEFAULT 0,
    completed_trips INTEGER DEFAULT 0,
    driving_license_url TEXT,
    id_card_url TEXT,
    vehicle_info TEXT,
    intro_video_url TEXT,
    previous_trip_images TEXT[] DEFAULT '{}',
    can_organize_women_only_trips BOOLEAN DEFAULT FALSE,
    can_organize_family_trips BOOLEAN DEFAULT FALSE
);

-- Trips table
CREATE TABLE IF NOT EXISTS trips (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    leader_id UUID REFERENCES users(id) ON DELETE CASCADE,
    leader_name TEXT NOT NULL,
    leader_image_url TEXT,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    from_city TEXT NOT NULL,
    to_destination TEXT NOT NULL,
    departure_date TIMESTAMP WITH TIME ZONE NOT NULL,
    departure_time TEXT NOT NULL,
    meeting_point TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    total_seats INTEGER NOT NULL,
    available_seats INTEGER NOT NULL,
    image_urls TEXT[] DEFAULT '{}',
    vehicle_type TEXT NOT NULL,
    vehicle_image_url TEXT NOT NULL,
    trip_type TEXT DEFAULT 'general' CHECK (trip_type IN ('general', 'family', 'women_only')),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
    rules TEXT[] DEFAULT '{}',
    program JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    rating DECIMAL(2,1) DEFAULT 0.0,
    review_count INTEGER DEFAULT 0,
    tags TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    return_time TEXT,
    estimated_duration DECIMAL(4,2),
    amenities TEXT[] DEFAULT '{}',
    allows_refund BOOLEAN DEFAULT FALSE,
    refund_deadline TIMESTAMP WITH TIME ZONE
);

-- Bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trip_id UUID REFERENCES trips(id) ON DELETE CASCADE,
    traveler_id UUID REFERENCES users(id) ON DELETE CASCADE,
    traveler_name TEXT NOT NULL,
    traveler_image_url TEXT,
    traveler_phone TEXT NOT NULL,
    leader_id UUID REFERENCES users(id) ON DELETE CASCADE,
    trip_title TEXT NOT NULL,
    from_city TEXT NOT NULL,
    to_destination TEXT NOT NULL,
    departure_date TIMESTAMP WITH TIME ZONE NOT NULL,
    departure_time TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    seats_booked INTEGER DEFAULT 1,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    notes TEXT,
    cancellation_reason TEXT,
    cancellation_date TIMESTAMP WITH TIME ZONE,
    is_paid BOOLEAN DEFAULT FALSE,
    payment_method TEXT,
    payment_id TEXT,
    is_refunded BOOLEAN DEFAULT FALSE,
    refund_amount DECIMAL(10,2),
    refund_date TIMESTAMP WITH TIME ZONE,
    has_reviewed BOOLEAN DEFAULT FALSE,
    rating DECIMAL(2,1),
    review TEXT
);

-- Reviews table
CREATE TABLE IF NOT EXISTS reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trip_id UUID REFERENCES trips(id) ON DELETE CASCADE,
    booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    reviewer_name TEXT NOT NULL,
    reviewer_image_url TEXT,
    leader_id UUID REFERENCES users(id) ON DELETE CASCADE,
    leader_name TEXT NOT NULL,
    trip_title TEXT NOT NULL,
    rating DECIMAL(2,1) NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT NOT NULL,
    image_urls TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_visible BOOLEAN DEFAULT TRUE,
    is_reported BOOLEAN DEFAULT FALSE,
    helpful_count INTEGER DEFAULT 0,
    helpful_users UUID[] DEFAULT '{}',
    leader_response TEXT,
    leader_response_date TIMESTAMP WITH TIME ZONE
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    body TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_user_type ON users(user_type);
CREATE INDEX IF NOT EXISTS idx_users_city ON users(city);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

CREATE INDEX IF NOT EXISTS idx_trips_leader_id ON trips(leader_id);
CREATE INDEX IF NOT EXISTS idx_trips_from_city ON trips(from_city);
CREATE INDEX IF NOT EXISTS idx_trips_to_destination ON trips(to_destination);
CREATE INDEX IF NOT EXISTS idx_trips_departure_date ON trips(departure_date);
CREATE INDEX IF NOT EXISTS idx_trips_status ON trips(status);
CREATE INDEX IF NOT EXISTS idx_trips_is_active ON trips(is_active);

CREATE INDEX IF NOT EXISTS idx_bookings_trip_id ON bookings(trip_id);
CREATE INDEX IF NOT EXISTS idx_bookings_traveler_id ON bookings(traveler_id);
CREATE INDEX IF NOT EXISTS idx_bookings_leader_id ON bookings(leader_id);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);

CREATE INDEX IF NOT EXISTS idx_reviews_trip_id ON reviews(trip_id);
CREATE INDEX IF NOT EXISTS idx_reviews_reviewer_id ON reviews(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_reviews_leader_id ON reviews(leader_id);

CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);

-- Row Level Security (RLS) Policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Users can read their own data and public profiles
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Public profiles are viewable" ON users FOR SELECT USING (is_active = true);

-- Trips are readable by all authenticated users
CREATE POLICY "Trips are viewable by authenticated users" ON trips FOR SELECT TO authenticated USING (true);
CREATE POLICY "Leaders can manage their trips" ON trips FOR ALL USING (auth.uid() = leader_id);

-- Bookings are accessible by trip leader and traveler
CREATE POLICY "Bookings viewable by participants" ON bookings FOR SELECT USING (
    auth.uid() = traveler_id OR auth.uid() = leader_id
);
CREATE POLICY "Travelers can create bookings" ON bookings FOR INSERT WITH CHECK (auth.uid() = traveler_id);
CREATE POLICY "Participants can update bookings" ON bookings FOR UPDATE USING (
    auth.uid() = traveler_id OR auth.uid() = leader_id
);

-- Reviews are readable by all, writable by reviewer
CREATE POLICY "Reviews are publicly viewable" ON reviews FOR SELECT TO authenticated USING (is_visible = true);
CREATE POLICY "Reviewers can manage their reviews" ON reviews FOR ALL USING (auth.uid() = reviewer_id);

-- Notifications are private to user
CREATE POLICY "Users can view own notifications" ON notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own notifications" ON notifications FOR UPDATE USING (auth.uid() = user_id);

-- Storage buckets
INSERT INTO storage.buckets (id, name, public) VALUES 
('images', 'images', true),
('documents', 'documents', false);

-- Storage policies
CREATE POLICY "Images are publicly accessible" ON storage.objects FOR SELECT USING (bucket_id = 'images');
CREATE POLICY "Authenticated users can upload images" ON storage.objects FOR INSERT TO authenticated WITH CHECK (bucket_id = 'images');
CREATE POLICY "Users can update their own images" ON storage.objects FOR UPDATE TO authenticated USING (bucket_id = 'images');

CREATE POLICY "Documents are private" ON storage.objects FOR SELECT USING (bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]);
CREATE POLICY "Users can upload documents" ON storage.objects FOR INSERT TO authenticated WITH CHECK (bucket_id = 'documents');
CREATE POLICY "Users can update their documents" ON storage.objects FOR UPDATE TO authenticated USING (bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Functions for automatic timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic timestamps
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_trips_updated_at BEFORE UPDATE ON trips FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON reviews FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
