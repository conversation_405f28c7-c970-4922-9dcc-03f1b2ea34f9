import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../constants/app_constants.dart';
import '../config/supabase_config.dart';

class UserService {
  final SupabaseClient _supabase = SupabaseConfig.client;

  // Get user by ID
  Future<UserModel?> getUserById(String userId) async {
    try {
      final response = await _supabase
          .from(AppConstants.usersCollection)
          .select()
          .eq('id', userId)
          .single();

      return UserModel.fromMap(response);
    } catch (e) {
      throw 'خطأ في جلب بيانات المستخدم: ${e.toString()}';
    }
  }

  // Get user stream (simplified for now)
  Stream<UserModel?> getUserStream(String userId) {
    // Note: Supabase real-time subscriptions would be implemented here
    // For now, return a simple stream
    return Stream.fromFuture(getUserById(userId));
  }

  // Update user profile
  Future<void> updateUserProfile(String userId, Map<String, dynamic> data) async {
    try {
      data['updated_at'] = DateTime.now().toIso8601String();
      await _supabase
          .from(AppConstants.usersCollection)
          .update(data)
          .eq('id', userId);
    } catch (e) {
      throw 'خطأ في تحديث الملف الشخصي: ${e.toString()}';
    }
  }

  // Upload profile image
  Future<String> uploadProfileImage(String userId, File imageFile) async {
    try {
      final String fileName = 'profile_$userId.jpg';
      final String filePath = '${AppConstants.profileImagesPath}/$fileName';

      await _supabase.storage
          .from('images')
          .upload(filePath, imageFile);

      final String downloadUrl = _supabase.storage
          .from('images')
          .getPublicUrl(filePath);

      // Update user profile with new image URL
      await updateUserProfile(userId, {'profile_image_url': downloadUrl});

      return downloadUrl;
    } catch (e) {
      throw 'خطأ في رفع الصورة: ${e.toString()}';
    }
  }

  // Upload verification documents
  Future<Map<String, String>> uploadVerificationDocuments({
    required String userId,
    File? drivingLicenseFile,
    File? idCardFile,
  }) async {
    try {
      final Map<String, String> urls = {};

      if (drivingLicenseFile != null) {
        final String fileName = 'driving_license_$userId.jpg';
        final String filePath = '${AppConstants.verificationDocsPath}/$fileName';

        await _supabase.storage
            .from('documents')
            .upload(filePath, drivingLicenseFile);

        urls['driving_license_url'] = _supabase.storage
            .from('documents')
            .getPublicUrl(filePath);
      }

      if (idCardFile != null) {
        final String fileName = 'id_card_$userId.jpg';
        final String filePath = '${AppConstants.verificationDocsPath}/$fileName';

        await _supabase.storage
            .from('documents')
            .upload(filePath, idCardFile);

        urls['id_card_url'] = _supabase.storage
            .from('documents')
            .getPublicUrl(filePath);
      }

      // Update user profile with verification documents
      if (urls.isNotEmpty) {
        await updateUserProfile(userId, urls);
      }

      return urls;
    } catch (e) {
      throw 'خطأ في رفع وثائق التحقق: ${e.toString()}';
    }
  }

  // Upload previous trip images
  Future<List<String>> uploadPreviousTripImages(String userId, List<File> imageFiles) async {
    try {
      final List<String> urls = [];

      for (int i = 0; i < imageFiles.length; i++) {
        final String fileName = 'previous_trip_${userId}_$i.jpg';
        final String filePath = '${AppConstants.tripImagesPath}/previous/$fileName';

        await _supabase.storage
            .from('images')
            .upload(filePath, imageFiles[i]);

        final String downloadUrl = _supabase.storage
            .from('images')
            .getPublicUrl(filePath);
        urls.add(downloadUrl);
      }

      // Update user profile with previous trip images
      await updateUserProfile(userId, {'previous_trip_images': urls});

      return urls;
    } catch (e) {
      throw 'خطأ في رفع صور الرحلات السابقة: ${e.toString()}';
    }
  }

  // Add points to user
  Future<void> addPoints(String userId, int points) async {
    try {
      final user = await getUserById(userId);
      if (user != null) {
        final int newPoints = user.points + points;
        await updateUserProfile(userId, {'points': newPoints});
      }
    } catch (e) {
      throw 'خطأ في إضافة النقاط: ${e.toString()}';
    }
  }

  // Add badge to user
  Future<void> addBadge(String userId, String badge) async {
    try {
      final user = await getUserById(userId);
      if (user != null && !user.badges.contains(badge)) {
        final List<String> newBadges = [...user.badges, badge];
        await updateUserProfile(userId, {'badges': newBadges});
      }
    } catch (e) {
      throw 'خطأ في إضافة الشارة: ${e.toString()}';
    }
  }

  // Update user rating
  Future<void> updateUserRating(String userId, double newRating) async {
    try {
      await updateUserProfile(userId, {'rating': newRating});
    } catch (e) {
      throw 'خطأ في تحديث التقييم: ${e.toString()}';
    }
  }

  // Increment trip count
  Future<void> incrementTripCount(String userId, {bool completed = false}) async {
    try {
      final user = await getUserById(userId);
      if (user != null) {
        final Map<String, dynamic> updates = {
          'total_trips': user.totalTrips + 1,
        };

        if (completed) {
          updates['completed_trips'] = user.completedTrips + 1;
        }

        await updateUserProfile(userId, updates);
      }
    } catch (e) {
      throw 'خطأ في تحديث عدد الرحلات: ${e.toString()}';
    }
  }

  // Get top leaders
  Future<List<UserModel>> getTopLeaders({int limit = 10}) async {
    try {
      final response = await _supabase
          .from(AppConstants.usersCollection)
          .select()
          .eq('user_type', AppConstants.userTypeLeader)
          .eq('is_active', true)
          .order('rating', ascending: false)
          .order('completed_trips', ascending: false)
          .limit(limit);

      return (response as List)
          .map((data) => UserModel.fromMap(data))
          .toList();
    } catch (e) {
      throw 'خطأ في جلب أفضل قادة الرحلات: ${e.toString()}';
    }
  }

  // Search users
  Future<List<UserModel>> searchUsers({
    required String query,
    String? userType,
    String? city,
    int limit = 20,
  }) async {
    try {
      var queryBuilder = _supabase
          .from(AppConstants.usersCollection)
          .select()
          .eq('is_active', true);

      if (userType != null) {
        queryBuilder = queryBuilder.eq('user_type', userType);
      }

      if (city != null) {
        queryBuilder = queryBuilder.eq('city', city);
      }

      final response = await queryBuilder.limit(limit);

      final List<UserModel> users = (response as List)
          .map((data) => UserModel.fromMap(data))
          .where((user) =>
              user.name.toLowerCase().contains(query.toLowerCase()) ||
              user.email.toLowerCase().contains(query.toLowerCase()))
          .toList();

      return users;
    } catch (e) {
      throw 'خطأ في البحث عن المستخدمين: ${e.toString()}';
    }
  }

  // Check and award badges
  Future<void> checkAndAwardBadges(String userId) async {
    try {
      final UserModel? user = await getUserById(userId);
      if (user == null) return;

      final List<String> newBadges = [];

      // Top Organizer badge
      if (user.completedTrips >= AppConstants.topOrganizerTrips &&
          !user.badges.contains('top_organizer')) {
        newBadges.add('top_organizer');
      }

      // 5-Star Leader badge
      if (user.rating >= AppConstants.fiveStarLeaderRating &&
          !user.badges.contains('five_star_leader')) {
        newBadges.add('five_star_leader');
      }

      // 100+ Trips badge
      if (user.totalTrips >= AppConstants.hundredTripsThreshold &&
          !user.badges.contains('hundred_trips')) {
        newBadges.add('hundred_trips');
      }

      // Award new badges
      for (String badge in newBadges) {
        await addBadge(userId, badge);
      }
    } catch (e) {
      // Silently fail badge checking
    }
  }

  // Deactivate user account
  Future<void> deactivateUser(String userId) async {
    try {
      await updateUserProfile(userId, {'isActive': false});
    } catch (e) {
      throw 'خطأ في إلغاء تفعيل الحساب: ${e.toString()}';
    }
  }

  // Reactivate user account
  Future<void> reactivateUser(String userId) async {
    try {
      await updateUserProfile(userId, {'isActive': true});
    } catch (e) {
      throw 'خطأ في إعادة تفعيل الحساب: ${e.toString()}';
    }
  }
}
