import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../models/user_model.dart';
import '../constants/app_constants.dart';

class UserService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Get user by ID
  Future<UserModel?> getUserById(String userId) async {
    try {
      final DocumentSnapshot doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        return UserModel.fromDocument(doc);
      }
    } catch (e) {
      throw 'خطأ في جلب بيانات المستخدم: ${e.toString()}';
    }
    return null;
  }

  // Get user stream
  Stream<UserModel?> getUserStream(String userId) {
    return _firestore
        .collection(AppConstants.usersCollection)
        .doc(userId)
        .snapshots()
        .map((doc) => doc.exists ? UserModel.fromDocument(doc) : null);
  }

  // Update user profile
  Future<void> updateUserProfile(String userId, Map<String, dynamic> data) async {
    try {
      data['updatedAt'] = Timestamp.now();
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update(data);
    } catch (e) {
      throw 'خطأ في تحديث الملف الشخصي: ${e.toString()}';
    }
  }

  // Upload profile image
  Future<String> uploadProfileImage(String userId, File imageFile) async {
    try {
      final String fileName = 'profile_$userId.jpg';
      final Reference ref = _storage
          .ref()
          .child(AppConstants.profileImagesPath)
          .child(fileName);

      final UploadTask uploadTask = ref.putFile(imageFile);
      final TaskSnapshot snapshot = await uploadTask;
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      // Update user profile with new image URL
      await updateUserProfile(userId, {'profileImageUrl': downloadUrl});

      return downloadUrl;
    } catch (e) {
      throw 'خطأ في رفع الصورة: ${e.toString()}';
    }
  }

  // Upload verification documents
  Future<Map<String, String>> uploadVerificationDocuments({
    required String userId,
    File? drivingLicenseFile,
    File? idCardFile,
  }) async {
    try {
      final Map<String, String> urls = {};

      if (drivingLicenseFile != null) {
        final String fileName = 'driving_license_$userId.jpg';
        final Reference ref = _storage
            .ref()
            .child(AppConstants.verificationDocsPath)
            .child(fileName);

        final UploadTask uploadTask = ref.putFile(drivingLicenseFile);
        final TaskSnapshot snapshot = await uploadTask;
        urls['drivingLicenseUrl'] = await snapshot.ref.getDownloadURL();
      }

      if (idCardFile != null) {
        final String fileName = 'id_card_$userId.jpg';
        final Reference ref = _storage
            .ref()
            .child(AppConstants.verificationDocsPath)
            .child(fileName);

        final UploadTask uploadTask = ref.putFile(idCardFile);
        final TaskSnapshot snapshot = await uploadTask;
        urls['idCardUrl'] = await snapshot.ref.getDownloadURL();
      }

      // Update user profile with verification documents
      if (urls.isNotEmpty) {
        await updateUserProfile(userId, urls);
      }

      return urls;
    } catch (e) {
      throw 'خطأ في رفع وثائق التحقق: ${e.toString()}';
    }
  }

  // Upload previous trip images
  Future<List<String>> uploadPreviousTripImages(String userId, List<File> imageFiles) async {
    try {
      final List<String> urls = [];

      for (int i = 0; i < imageFiles.length; i++) {
        final String fileName = 'previous_trip_${userId}_$i.jpg';
        final Reference ref = _storage
            .ref()
            .child(AppConstants.tripImagesPath)
            .child('previous')
            .child(fileName);

        final UploadTask uploadTask = ref.putFile(imageFiles[i]);
        final TaskSnapshot snapshot = await uploadTask;
        final String downloadUrl = await snapshot.ref.getDownloadURL();
        urls.add(downloadUrl);
      }

      // Update user profile with previous trip images
      await updateUserProfile(userId, {'previousTripImages': urls});

      return urls;
    } catch (e) {
      throw 'خطأ في رفع صور الرحلات السابقة: ${e.toString()}';
    }
  }

  // Add points to user
  Future<void> addPoints(String userId, int points) async {
    try {
      await _firestore.runTransaction((transaction) async {
        final DocumentReference userRef = _firestore
            .collection(AppConstants.usersCollection)
            .doc(userId);

        final DocumentSnapshot userDoc = await transaction.get(userRef);
        if (userDoc.exists) {
          final UserModel user = UserModel.fromDocument(userDoc);
          final int newPoints = user.points + points;
          
          transaction.update(userRef, {
            'points': newPoints,
            'updatedAt': Timestamp.now(),
          });
        }
      });
    } catch (e) {
      throw 'خطأ في إضافة النقاط: ${e.toString()}';
    }
  }

  // Add badge to user
  Future<void> addBadge(String userId, String badge) async {
    try {
      await _firestore.runTransaction((transaction) async {
        final DocumentReference userRef = _firestore
            .collection(AppConstants.usersCollection)
            .doc(userId);

        final DocumentSnapshot userDoc = await transaction.get(userRef);
        if (userDoc.exists) {
          final UserModel user = UserModel.fromDocument(userDoc);
          if (!user.badges.contains(badge)) {
            final List<String> newBadges = [...user.badges, badge];
            
            transaction.update(userRef, {
              'badges': newBadges,
              'updatedAt': Timestamp.now(),
            });
          }
        }
      });
    } catch (e) {
      throw 'خطأ في إضافة الشارة: ${e.toString()}';
    }
  }

  // Update user rating
  Future<void> updateUserRating(String userId, double newRating) async {
    try {
      await updateUserProfile(userId, {'rating': newRating});
    } catch (e) {
      throw 'خطأ في تحديث التقييم: ${e.toString()}';
    }
  }

  // Increment trip count
  Future<void> incrementTripCount(String userId, {bool completed = false}) async {
    try {
      await _firestore.runTransaction((transaction) async {
        final DocumentReference userRef = _firestore
            .collection(AppConstants.usersCollection)
            .doc(userId);

        final DocumentSnapshot userDoc = await transaction.get(userRef);
        if (userDoc.exists) {
          final UserModel user = UserModel.fromDocument(userDoc);
          final Map<String, dynamic> updates = {
            'totalTrips': user.totalTrips + 1,
            'updatedAt': Timestamp.now(),
          };

          if (completed) {
            updates['completedTrips'] = user.completedTrips + 1;
          }
          
          transaction.update(userRef, updates);
        }
      });
    } catch (e) {
      throw 'خطأ في تحديث عدد الرحلات: ${e.toString()}';
    }
  }

  // Get top leaders
  Future<List<UserModel>> getTopLeaders({int limit = 10}) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('userType', isEqualTo: AppConstants.userTypeLeader)
          .where('isActive', isEqualTo: true)
          .orderBy('rating', descending: true)
          .orderBy('completedTrips', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw 'خطأ في جلب أفضل قادة الرحلات: ${e.toString()}';
    }
  }

  // Search users
  Future<List<UserModel>> searchUsers({
    required String query,
    String? userType,
    String? city,
    int limit = 20,
  }) async {
    try {
      Query queryRef = _firestore
          .collection(AppConstants.usersCollection)
          .where('isActive', isEqualTo: true);

      if (userType != null) {
        queryRef = queryRef.where('userType', isEqualTo: userType);
      }

      if (city != null) {
        queryRef = queryRef.where('city', isEqualTo: city);
      }

      final QuerySnapshot snapshot = await queryRef
          .limit(limit)
          .get();

      final List<UserModel> users = snapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .where((user) => 
              user.name.toLowerCase().contains(query.toLowerCase()) ||
              user.email.toLowerCase().contains(query.toLowerCase()))
          .toList();

      return users;
    } catch (e) {
      throw 'خطأ في البحث عن المستخدمين: ${e.toString()}';
    }
  }

  // Check and award badges
  Future<void> checkAndAwardBadges(String userId) async {
    try {
      final UserModel? user = await getUserById(userId);
      if (user == null) return;

      final List<String> newBadges = [];

      // Top Organizer badge
      if (user.completedTrips >= AppConstants.topOrganizerTrips && 
          !user.badges.contains('top_organizer')) {
        newBadges.add('top_organizer');
      }

      // 5-Star Leader badge
      if (user.rating >= AppConstants.fiveStarLeaderRating && 
          user.reviewCount >= 10 &&
          !user.badges.contains('five_star_leader')) {
        newBadges.add('five_star_leader');
      }

      // 100+ Trips badge
      if (user.totalTrips >= AppConstants.hundredTripsThreshold && 
          !user.badges.contains('hundred_trips')) {
        newBadges.add('hundred_trips');
      }

      // Award new badges
      for (String badge in newBadges) {
        await addBadge(userId, badge);
      }
    } catch (e) {
      // Silently fail badge checking
    }
  }

  // Deactivate user account
  Future<void> deactivateUser(String userId) async {
    try {
      await updateUserProfile(userId, {'isActive': false});
    } catch (e) {
      throw 'خطأ في إلغاء تفعيل الحساب: ${e.toString()}';
    }
  }

  // Reactivate user account
  Future<void> reactivateUser(String userId) async {
    try {
      await updateUserProfile(userId, {'isActive': true});
    } catch (e) {
      throw 'خطأ في إعادة تفعيل الحساب: ${e.toString()}';
    }
  }
}
