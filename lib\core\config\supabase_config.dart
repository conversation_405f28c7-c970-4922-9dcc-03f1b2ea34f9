import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseConfig {
  static const String supabaseUrl = 'https://pmykpbrecfoxgtahetgb.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBteWtwYnJlY2ZveGd0YWhldGdiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0NTU0NzQsImV4cCI6MjA1MDAzMTQ3NH0.example'; // Replace with actual anon key

  static Future<void> initialize() async {
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      debug: true, // Set to false in production
    );
  }

  static SupabaseClient get client => Supabase.instance.client;
  static GoTrueClient get auth => client.auth;
  static SupabaseStorageClient get storage => client.storage;
  static SupabaseQueryBuilder get from => client.from;
}