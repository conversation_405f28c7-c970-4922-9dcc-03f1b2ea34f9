name: <PERSON><PERSON><PERSON>_app
description: "سفرني - تطبيق السفر الجماعي في المغرب"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Navigation
  cupertino_icons: ^1.0.2
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.1
  
  # State Management
  provider: ^6.1.1

  # Backend & Database
  supabase_flutter: ^2.3.4
  
  # Image & Media
  image_picker: ^1.0.4
  cached_network_image: ^3.3.0
  photo_view: ^0.14.0
  video_player: ^2.8.1
  
  # UI Components
  flutter_rating_bar: ^4.0.1
  carousel_slider: ^4.2.1
  shimmer: ^3.0.0
  lottie: ^2.7.0
  
  # Utilities
  shared_preferences: ^2.2.2
  url_launcher: ^6.2.2
  permission_handler: ^11.1.0
  geolocator: ^10.1.0
  uuid: ^4.2.1
  
  # Date & Time
  jiffy: ^6.2.1
  
  # HTTP & Networking
  http: ^1.1.2
  
  # Local Notifications
  flutter_local_notifications: ^16.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
  
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
    - family: Amiri
      fonts:
        - asset: assets/fonts/Amiri-Regular.ttf
        - asset: assets/fonts/Amiri-Bold.ttf
          weight: 700
