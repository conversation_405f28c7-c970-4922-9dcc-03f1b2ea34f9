import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../constants/app_constants.dart';
import '../config/supabase_config.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  static final SupabaseClient _supabase = SupabaseConfig.client;

  // Initialize notifications
  static Future<void> initialize() async {
    // Initialize local notifications only
    try {
      if (kDebugMode) {
        print('Initializing local notifications...');
      }
      // Basic initialization - will be properly configured when dependencies are available
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing notifications: $e');
      }
    }
  }

  // Show local notification
  static Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      if (kDebugMode) {
        print('Showing notification: $title - $body');
      }
      // Local notification implementation will be added when dependencies are available
    } catch (e) {
      if (kDebugMode) {
        print('Error showing notification: $e');
      }
    }
  }



  // Send notification to user
  static Future<void> sendNotificationToUser({
    required String userId,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Save notification to Supabase
      await _supabase
          .from(AppConstants.notificationsCollection)
          .insert({
        'user_id': userId,
        'title': title,
        'body': body,
        'data': data ?? {},
        'is_read': false,
        'created_at': DateTime.now().toIso8601String(),
      });

      // Show local notification
      await showLocalNotification(
        title: title,
        body: body,
        payload: data.toString(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error sending notification: $e');
      }
    }
  }

  // Send booking notification
  static Future<void> sendBookingNotification({
    required String userId,
    required String tripTitle,
    required String status,
    String? tripId,
  }) async {
    String title = '';
    String body = '';

    switch (status) {
      case 'accepted':
        title = 'تم قبول حجزك';
        body = 'تم قبول حجزك للرحلة: $tripTitle';
        break;
      case 'rejected':
        title = 'تم رفض حجزك';
        body = 'تم رفض حجزك للرحلة: $tripTitle';
        break;
      case 'cancelled':
        title = 'تم إلغاء الرحلة';
        body = 'تم إلغاء الرحلة: $tripTitle';
        break;
    }

    await sendNotificationToUser(
      userId: userId,
      title: title,
      body: body,
      data: {
        'type': 'booking',
        'status': status,
        'tripId': tripId,
      },
    );
  }

  // Send trip reminder
  static Future<void> sendTripReminder({
    required String userId,
    required String tripTitle,
    required DateTime departureTime,
    String? tripId,
  }) async {
    await sendNotificationToUser(
      userId: userId,
      title: 'تذكير بالرحلة',
      body: 'رحلتك "$tripTitle" ستبدأ غداً في ${departureTime.hour}:${departureTime.minute.toString().padLeft(2, '0')}',
      data: {
        'type': 'reminder',
        'tripId': tripId,
      },
    );
  }

  // Send new message notification
  static Future<void> sendNewMessageNotification({
    required String userId,
    required String senderName,
    required String message,
    String? chatId,
  }) async {
    await sendNotificationToUser(
      userId: userId,
      title: 'رسالة جديدة من $senderName',
      body: message,
      data: {
        'type': 'message',
        'chatId': chatId,
      },
    );
  }

  // Mark notification as read
  static Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _supabase
          .from(AppConstants.notificationsCollection)
          .update({'is_read': true})
          .eq('id', notificationId);
    } catch (e) {
      if (kDebugMode) {
        print('Error marking notification as read: $e');
      }
    }
  }

  // Clear all notifications for user
  static Future<void> clearAllNotifications(String userId) async {
    try {
      await _supabase
          .from(AppConstants.notificationsCollection)
          .delete()
          .eq('user_id', userId);
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing notifications: $e');
      }
    }
  }
}
