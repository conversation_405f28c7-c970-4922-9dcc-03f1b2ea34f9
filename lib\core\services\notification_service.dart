import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _localNotifications = 
      FlutterLocalNotificationsPlugin();
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Initialize notifications
  static Future<void> initialize() async {
    // Initialize local notifications
    const AndroidInitializationSettings androidSettings = 
        AndroidInitializationSettings('@mipmap/ic_launcher');
    
    const DarwinInitializationSettings iosSettings = 
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );

    const InitializationSettings settings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      settings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Initialize Firebase messaging
    await _initializeFirebaseMessaging();
  }

  // Initialize Firebase messaging
  static Future<void> _initializeFirebaseMessaging() async {
    // Request permission
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      if (kDebugMode) {
        print('User granted permission');
      }
    }

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Handle notification tap when app is terminated
    RemoteMessage? initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      _handleNotificationTap(initialMessage);
    }
  }

  // Handle foreground messages
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    if (kDebugMode) {
      print('Received foreground message: ${message.messageId}');
    }

    // Show local notification
    await _showLocalNotification(
      title: message.notification?.title ?? 'سفرني',
      body: message.notification?.body ?? '',
      payload: message.data.toString(),
    );
  }

  // Handle background messages
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    if (kDebugMode) {
      print('Received background message: ${message.messageId}');
    }
  }

  // Handle notification tap
  static void _handleNotificationTap(RemoteMessage message) {
    if (kDebugMode) {
      print('Notification tapped: ${message.data}');
    }
    // Navigate to appropriate screen based on notification data
    _navigateBasedOnNotification(message.data);
  }

  // Handle local notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    if (kDebugMode) {
      print('Local notification tapped: ${response.payload}');
    }
    // Handle local notification tap
  }

  // Show local notification
  static Future<void> _showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'safarni_channel',
      'سفرني',
      channelDescription: 'إشعارات تطبيق سفرني',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title,
      body,
      details,
      payload: payload,
    );
  }

  // Get FCM token
  static Future<String?> getFCMToken() async {
    try {
      return await _firebaseMessaging.getToken();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting FCM token: $e');
      }
      return null;
    }
  }

  // Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
    } catch (e) {
      if (kDebugMode) {
        print('Error subscribing to topic: $e');
      }
    }
  }

  // Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
    } catch (e) {
      if (kDebugMode) {
        print('Error unsubscribing from topic: $e');
      }
    }
  }

  // Send notification to user
  static Future<void> sendNotificationToUser({
    required String userId,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Save notification to Firestore
      await _firestore
          .collection(AppConstants.notificationsCollection)
          .add({
        'userId': userId,
        'title': title,
        'body': body,
        'data': data ?? {},
        'isRead': false,
        'createdAt': Timestamp.now(),
      });

      // Note: In a real app, you would send this through your backend
      // to Firebase Cloud Messaging API
    } catch (e) {
      if (kDebugMode) {
        print('Error sending notification: $e');
      }
    }
  }

  // Send booking notification
  static Future<void> sendBookingNotification({
    required String userId,
    required String tripTitle,
    required String status,
    String? tripId,
  }) async {
    String title = '';
    String body = '';

    switch (status) {
      case 'accepted':
        title = 'تم قبول حجزك';
        body = 'تم قبول حجزك للرحلة: $tripTitle';
        break;
      case 'rejected':
        title = 'تم رفض حجزك';
        body = 'تم رفض حجزك للرحلة: $tripTitle';
        break;
      case 'cancelled':
        title = 'تم إلغاء الرحلة';
        body = 'تم إلغاء الرحلة: $tripTitle';
        break;
    }

    await sendNotificationToUser(
      userId: userId,
      title: title,
      body: body,
      data: {
        'type': 'booking',
        'status': status,
        'tripId': tripId,
      },
    );
  }

  // Send trip reminder
  static Future<void> sendTripReminder({
    required String userId,
    required String tripTitle,
    required DateTime departureTime,
    String? tripId,
  }) async {
    await sendNotificationToUser(
      userId: userId,
      title: 'تذكير بالرحلة',
      body: 'رحلتك "$tripTitle" ستبدأ غداً في ${_formatTime(departureTime)}',
      data: {
        'type': 'reminder',
        'tripId': tripId,
      },
    );
  }

  // Send new message notification
  static Future<void> sendNewMessageNotification({
    required String userId,
    required String senderName,
    required String message,
    String? chatId,
  }) async {
    await sendNotificationToUser(
      userId: userId,
      title: 'رسالة جديدة من $senderName',
      body: message,
      data: {
        'type': 'message',
        'chatId': chatId,
      },
    );
  }

  // Schedule trip reminder
  static Future<void> scheduleTripReminder({
    required String tripId,
    required String tripTitle,
    required DateTime departureTime,
  }) async {
    // Schedule notification 24 hours before departure
    final DateTime reminderTime = departureTime.subtract(const Duration(hours: 24));
    
    if (reminderTime.isAfter(DateTime.now())) {
      // In a real app, you would use a background job scheduler
      // For now, we'll just save it to Firestore for manual processing
      await _firestore
          .collection('scheduled_notifications')
          .add({
        'tripId': tripId,
        'tripTitle': tripTitle,
        'departureTime': Timestamp.fromDate(departureTime),
        'reminderTime': Timestamp.fromDate(reminderTime),
        'type': 'trip_reminder',
        'processed': false,
        'createdAt': Timestamp.now(),
      });
    }
  }

  // Navigate based on notification
  static void _navigateBasedOnNotification(Map<String, dynamic> data) {
    final String? type = data['type'];
    
    switch (type) {
      case 'booking':
        // Navigate to bookings screen
        break;
      case 'message':
        // Navigate to chat screen
        break;
      case 'reminder':
        // Navigate to trip details screen
        break;
      default:
        // Navigate to home screen
        break;
    }
  }

  // Format time for display
  static String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // Get user notifications
  static Stream<QuerySnapshot> getUserNotifications(String userId) {
    return _firestore
        .collection(AppConstants.notificationsCollection)
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .limit(50)
        .snapshots();
  }

  // Mark notification as read
  static Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _firestore
          .collection(AppConstants.notificationsCollection)
          .doc(notificationId)
          .update({'isRead': true});
    } catch (e) {
      if (kDebugMode) {
        print('Error marking notification as read: $e');
      }
    }
  }

  // Clear all notifications for user
  static Future<void> clearAllNotifications(String userId) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection(AppConstants.notificationsCollection)
          .where('userId', isEqualTo: userId)
          .get();

      final WriteBatch batch = _firestore.batch();
      for (var doc in snapshot.docs) {
        batch.delete(doc.reference);
      }
      await batch.commit();
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing notifications: $e');
      }
    }
  }
}
