class AppConstants {
  // App Info
  static const String appName = 'سفرني';
  static const String appSlogan = 'سافر جماعة، بتمن مناسب، بأمان تام!';
  static const String appVersion = '1.0.0';
  
  // User Types
  static const String userTypeTraveler = 'traveler';
  static const String userTypeLeader = 'leader';
  
  // Trip Status
  static const String tripStatusActive = 'active';
  static const String tripStatusCompleted = 'completed';
  static const String tripStatusCancelled = 'cancelled';
  
  // Booking Status
  static const String bookingStatusPending = 'pending';
  static const String bookingStatusAccepted = 'accepted';
  static const String bookingStatusRejected = 'rejected';
  static const String bookingStatusCancelled = 'cancelled';
  
  // Trip Types
  static const String tripTypeGeneral = 'general';
  static const String tripTypeFamily = 'family';
  static const String tripTypeWomenOnly = 'women_only';
  
  // Collections
  static const String usersCollection = 'users';
  static const String tripsCollection = 'trips';
  static const String bookingsCollection = 'bookings';
  static const String reviewsCollection = 'reviews';
  static const String chatsCollection = 'chats';
  static const String messagesCollection = 'messages';
  static const String notificationsCollection = 'notifications';
  
  // Storage Paths
  static const String profileImagesPath = 'profile_images';
  static const String tripImagesPath = 'trip_images';
  static const String verificationDocsPath = 'verification_docs';
  static const String chatImagesPath = 'chat_images';
  
  // Moroccan Cities
  static const List<String> moroccanCities = [
    'الرباط',
    'الدار البيضاء',
    'فاس',
    'مراكش',
    'أكادير',
    'طنجة',
    'مكناس',
    'وجدة',
    'القنيطرة',
    'تطوان',
    'سلا',
    'المحمدية',
    'خريبكة',
    'جديدة',
    'تازة',
    'سطات',
    'خنيفرة',
    'الناظور',
    'بركان',
    'الحسيمة',
  ];
  
  // Popular Destinations
  static const List<String> popularDestinations = [
    'شفشاون',
    'إفران',
    'الصويرة',
    'ورزازات',
    'مرزوقة',
    'أزيلال',
    'إميلشيل',
    'أوكايمدن',
    'الحسيمة',
    'أصيلة',
    'العرائش',
    'الجديدة',
    'آسفي',
    'تيزنيت',
    'تارودانت',
  ];
  
  // Validation
  static const int minPasswordLength = 6;
  static const int maxTripNameLength = 100;
  static const int maxTripDescriptionLength = 500;
  static const int maxMessageLength = 1000;
  
  // Pagination
  static const int tripsPerPage = 10;
  static const int messagesPerPage = 20;
  static const int reviewsPerPage = 10;
  
  // Points System
  static const int pointsPerBooking = 10;
  static const int pointsPerReview = 5;
  static const int pointsPerTripCompletion = 20;
  
  // Badge Thresholds
  static const int topOrganizerTrips = 50;
  static const int fiveStarLeaderRating = 5;
  static const int hundredTripsThreshold = 100;
}
