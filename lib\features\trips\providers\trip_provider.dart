import 'package:flutter/foundation.dart';
import '../../../core/models/trip_model.dart';
import '../../../core/services/trip_service.dart';

class TripProvider extends ChangeNotifier {
  final TripService _tripService;
  
  List<TripModel> _trips = [];
  List<TripModel> _userTrips = [];
  bool _isLoading = false;
  String? _error;

  TripProvider(this._tripService);

  // Getters
  List<TripModel> get trips => _trips;
  List<TripModel> get userTrips => _userTrips;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load trips
  Future<void> loadTrips() async {
    try {
      _setLoading(true);
      _clearError();
      
      _trips = await _tripService.getTrips();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
