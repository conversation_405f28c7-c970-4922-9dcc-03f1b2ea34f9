import 'package:flutter/foundation.dart';
import '../../../core/models/user_model.dart';
import '../../../core/services/user_service.dart';

class UserProvider extends ChangeNotifier {
  final UserService _userService;
  
  UserModel? _currentUser;
  bool _isLoading = false;
  String? _error;

  UserProvider(this._userService);

  // Getters
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load user
  Future<void> loadUser(String userId) async {
    try {
      _setLoading(true);
      _clearError();
      
      _currentUser = await _userService.getUserById(userId);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
