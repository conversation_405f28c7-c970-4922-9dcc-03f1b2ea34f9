# سفرني | Safarni

**سافر جماعة، بتمن مناسب، بأمان تام!**

A collective travel app for Morocco that connects passengers with trip organizers and vehicle owners. The goal is to enable safe, organized, and affordable group travel throughout the year.

## Features

### For Trip Leaders (قائد الرحلة)
- ✅ Profile creation with verification
- ✅ Trip creation and management
- ✅ Booking management (accept/reject)
- ✅ Chat with travelers
- ✅ Upload trip galleries
- ✅ Vehicle information and images
- ✅ Trip program scheduling
- ✅ Reviews and ratings system

### For Travelers (المسافر)
- ✅ Browse available trips
- ✅ Advanced filtering system
- ✅ Trip booking and confirmation
- ✅ Chat with trip leaders
- ✅ Trip history and reviews
- ✅ Notifications system
- ✅ Profile management

### Special Features
- 🔐 **Leader Verification**: ID card and driving license upload
- 👨‍👩‍👧‍👦 **Family Trips**: Special family-only trip categories
- 👩 **Women-Only Trips**: Safe travel options for women
- 🎯 **Trip Requests**: Users can suggest destinations
- 🏆 **Gamification**: Badges, points, and rewards system
- 💬 **Real-time Chat**: In-app messaging system
- 📱 **Push Notifications**: Booking updates and reminders
- 🌟 **Reviews & Ratings**: Public feedback system

## Tech Stack

- **Frontend**: Flutter (Dart)
- **Backend**: Firebase (Firestore, Auth, Storage, Messaging)
- **State Management**: Provider
- **UI/UX**: Material Design with Moroccan-inspired theme
- **Localization**: Arabic RTL support
- **Maps**: Google Maps integration
- **Notifications**: Firebase Cloud Messaging + Local Notifications

## Project Structure

```
lib/
├── core/
│   ├── constants/          # App constants and configurations
│   ├── models/            # Data models (User, Trip, Booking, Review)
│   ├── services/          # Business logic services
│   └── theme/             # App theming and styling
├── features/
│   ├── auth/              # Authentication screens and logic
│   ├── home/              # Home screen and navigation
│   ├── trips/             # Trip-related features
│   ├── profile/           # User profile management
│   ├── chat/              # Messaging system
│   └── notifications/     # Notification handling
└── main.dart              # App entry point
```

## Getting Started

### Prerequisites
- Flutter SDK (3.0.0 or higher)
- Dart SDK
- Android Studio / VS Code
- Firebase project setup

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd safarni
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Setup**
   - Create a new Firebase project
   - Enable Authentication, Firestore, Storage, and Messaging
   - Download `google-services.json` for Android
   - Download `GoogleService-Info.plist` for iOS
   - Update `lib/firebase_options.dart` with your configuration

4. **Add Arabic Fonts**
   - Download Cairo and Amiri fonts
   - Place them in `assets/fonts/`
   - Fonts are already configured in `pubspec.yaml`

5. **Run the app**
   ```bash
   flutter run
   ```

## Configuration

### Firebase Security Rules

**Firestore Rules:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Trips are readable by all authenticated users
    match /trips/{tripId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (resource == null || resource.data.leaderId == request.auth.uid);
    }
    
    // Bookings are readable by trip leader and traveler
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null && 
        (resource.data.travelerId == request.auth.uid || 
         resource.data.leaderId == request.auth.uid);
    }
  }
}
```

**Storage Rules:**
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /profile_images/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /trip_images/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
  }
}
```

## Features Implementation Status

### ✅ Completed
- [x] Project setup and structure
- [x] Authentication system foundation
- [x] Core data models
- [x] Basic UI components and theming
- [x] Firebase integration setup
- [x] Notification service foundation

### 🚧 In Progress
- [ ] Complete authentication screens
- [ ] Trip creation and management
- [ ] Booking system
- [ ] Chat functionality
- [ ] Image upload and galleries

### 📋 Planned
- [ ] Advanced filtering
- [ ] Gamification system
- [ ] Trip requests feature
- [ ] Payment integration
- [ ] Maps integration
- [ ] Comprehensive testing

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contact

For questions or support, please contact the development team.

---

**سفرني - تطبيق السفر الجماعي في المغرب**
