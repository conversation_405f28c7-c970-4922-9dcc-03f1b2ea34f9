import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../constants/app_constants.dart';
import '../config/supabase_config.dart';

class AuthService {
  final SupabaseClient _supabase = SupabaseConfig.client;

  // Get current user
  User? get currentUser => _supabase.auth.currentUser;

  // Get current user stream
  Stream<AuthState> get authStateChanges => _supabase.auth.onAuthStateChange;

  // Sign up with email and password
  Future<UserModel?> signUpWithEmail({
    required String email,
    required String password,
    required String name,
    required String city,
    required String userType,
    String? phoneNumber,
  }) async {
    try {
      // Create user with email and password
      final AuthResponse response = await _supabase.auth.signUp(
        email: email,
        password: password,
      );

      final User? user = response.user;
      if (user != null) {
        // Create user document in Supabase
        final UserModel userModel = UserModel(
          id: user.id,
          name: name,
          email: email,
          phoneNumber: phoneNumber,
          userType: userType,
          city: city,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _supabase
            .from(AppConstants.usersCollection)
            .insert(userModel.toMap());

        return userModel;
      }
    } catch (e) {
      throw _handleAuthException(e);
    }
    return null;
  }

  // Sign in with email and password
  Future<UserModel?> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final AuthResponse response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      final User? user = response.user;
      if (user != null) {
        // Get user data from Supabase
        final response = await _supabase
            .from(AppConstants.usersCollection)
            .select()
            .eq('id', user.id)
            .single();

        return UserModel.fromMap(response);
      }
    } catch (e) {
      throw _handleAuthException(e);
    }
    return null;
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _supabase.auth.signOut();
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _supabase.auth.resetPasswordForEmail(email);
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Handle authentication exceptions
  String _handleAuthException(dynamic e) {
    if (e is AuthException) {
      switch (e.message) {
        case 'Invalid login credentials':
          return 'بيانات تسجيل الدخول غير صحيحة';
        case 'User already registered':
          return 'البريد الإلكتروني مستخدم بالفعل';
        case 'Password should be at least 6 characters':
          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        case 'Unable to validate email address: invalid format':
          return 'البريد الإلكتروني غير صحيح';
        case 'Email not confirmed':
          return 'يرجى تأكيد البريد الإلكتروني';
        default:
          return e.message;
      }
    }
    return e.toString();
  }
}
