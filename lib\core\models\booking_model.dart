import 'package:cloud_firestore/cloud_firestore.dart';

class BookingModel {
  final String id;
  final String tripId; // معرف الرحلة
  final String travelerId; // معرف المسافر
  final String travelerName; // اسم المسافر
  final String travelerImageUrl; // صورة المسافر
  final String travelerPhone; // هاتف المسافر
  final String leaderId; // معرف قائد الرحلة
  final String tripTitle; // عنوان الرحلة
  final String fromCity; // من مدينة
  final String toDestination; // إلى وجهة
  final DateTime departureDate; // تاريخ المغادرة
  final String departureTime; // وقت المغادرة
  final double price; // السعر
  final int seatsBooked; // عدد المقاعد المحجوزة
  final String status; // حالة الحجز
  final DateTime createdAt; // تاريخ الإنشاء
  final DateTime updatedAt; // تاريخ التحديث
  final String? notes; // ملاحظات
  final String? cancellationReason; // سبب الإلغاء
  final DateTime? cancellationDate; // تاريخ الإلغاء
  final bool isPaid; // مدفوع
  final String? paymentMethod; // طريقة الدفع
  final String? paymentId; // معرف الدفع
  final bool isRefunded; // مسترد
  final double? refundAmount; // مبلغ الاسترداد
  final DateTime? refundDate; // تاريخ الاسترداد
  final bool hasReviewed; // تم التقييم
  final double? rating; // التقييم المعطى
  final String? review; // المراجعة

  BookingModel({
    required this.id,
    required this.tripId,
    required this.travelerId,
    required this.travelerName,
    required this.travelerImageUrl,
    required this.travelerPhone,
    required this.leaderId,
    required this.tripTitle,
    required this.fromCity,
    required this.toDestination,
    required this.departureDate,
    required this.departureTime,
    required this.price,
    this.seatsBooked = 1,
    this.status = 'pending',
    required this.createdAt,
    required this.updatedAt,
    this.notes,
    this.cancellationReason,
    this.cancellationDate,
    this.isPaid = false,
    this.paymentMethod,
    this.paymentId,
    this.isRefunded = false,
    this.refundAmount,
    this.refundDate,
    this.hasReviewed = false,
    this.rating,
    this.review,
  });

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'tripId': tripId,
      'travelerId': travelerId,
      'travelerName': travelerName,
      'travelerImageUrl': travelerImageUrl,
      'travelerPhone': travelerPhone,
      'leaderId': leaderId,
      'tripTitle': tripTitle,
      'fromCity': fromCity,
      'toDestination': toDestination,
      'departureDate': Timestamp.fromDate(departureDate),
      'departureTime': departureTime,
      'price': price,
      'seatsBooked': seatsBooked,
      'status': status,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'notes': notes,
      'cancellationReason': cancellationReason,
      'cancellationDate': cancellationDate != null ? Timestamp.fromDate(cancellationDate!) : null,
      'isPaid': isPaid,
      'paymentMethod': paymentMethod,
      'paymentId': paymentId,
      'isRefunded': isRefunded,
      'refundAmount': refundAmount,
      'refundDate': refundDate != null ? Timestamp.fromDate(refundDate!) : null,
      'hasReviewed': hasReviewed,
      'rating': rating,
      'review': review,
    };
  }

  // Create from Firestore document
  factory BookingModel.fromMap(Map<String, dynamic> map) {
    return BookingModel(
      id: map['id'] ?? '',
      tripId: map['tripId'] ?? '',
      travelerId: map['travelerId'] ?? '',
      travelerName: map['travelerName'] ?? '',
      travelerImageUrl: map['travelerImageUrl'] ?? '',
      travelerPhone: map['travelerPhone'] ?? '',
      leaderId: map['leaderId'] ?? '',
      tripTitle: map['tripTitle'] ?? '',
      fromCity: map['fromCity'] ?? '',
      toDestination: map['toDestination'] ?? '',
      departureDate: (map['departureDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      departureTime: map['departureTime'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
      seatsBooked: map['seatsBooked'] ?? 1,
      status: map['status'] ?? 'pending',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      notes: map['notes'],
      cancellationReason: map['cancellationReason'],
      cancellationDate: (map['cancellationDate'] as Timestamp?)?.toDate(),
      isPaid: map['isPaid'] ?? false,
      paymentMethod: map['paymentMethod'],
      paymentId: map['paymentId'],
      isRefunded: map['isRefunded'] ?? false,
      refundAmount: map['refundAmount']?.toDouble(),
      refundDate: (map['refundDate'] as Timestamp?)?.toDate(),
      hasReviewed: map['hasReviewed'] ?? false,
      rating: map['rating']?.toDouble(),
      review: map['review'],
    );
  }

  // Create from Firestore DocumentSnapshot
  factory BookingModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return BookingModel.fromMap({...data, 'id': doc.id});
  }

  // Copy with method for updates
  BookingModel copyWith({
    String? status,
    DateTime? updatedAt,
    String? notes,
    String? cancellationReason,
    DateTime? cancellationDate,
    bool? isPaid,
    String? paymentMethod,
    String? paymentId,
    bool? isRefunded,
    double? refundAmount,
    DateTime? refundDate,
    bool? hasReviewed,
    double? rating,
    String? review,
  }) {
    return BookingModel(
      id: id,
      tripId: tripId,
      travelerId: travelerId,
      travelerName: travelerName,
      travelerImageUrl: travelerImageUrl,
      travelerPhone: travelerPhone,
      leaderId: leaderId,
      tripTitle: tripTitle,
      fromCity: fromCity,
      toDestination: toDestination,
      departureDate: departureDate,
      departureTime: departureTime,
      price: price,
      seatsBooked: seatsBooked,
      status: status ?? this.status,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      notes: notes ?? this.notes,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      cancellationDate: cancellationDate ?? this.cancellationDate,
      isPaid: isPaid ?? this.isPaid,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentId: paymentId ?? this.paymentId,
      isRefunded: isRefunded ?? this.isRefunded,
      refundAmount: refundAmount ?? this.refundAmount,
      refundDate: refundDate ?? this.refundDate,
      hasReviewed: hasReviewed ?? this.hasReviewed,
      rating: rating ?? this.rating,
      review: review ?? this.review,
    );
  }

  // Status checks
  bool get isPending => status == 'pending';
  bool get isAccepted => status == 'accepted';
  bool get isRejected => status == 'rejected';
  bool get isCancelled => status == 'cancelled';

  // Calculate total amount
  double get totalAmount => price * seatsBooked;

  // Check if booking can be cancelled
  bool get canCancel {
    return (isPending || isAccepted) && 
           departureDate.isAfter(DateTime.now().add(const Duration(hours: 24)));
  }

  // Check if booking can be reviewed
  bool get canReview {
    return isAccepted && 
           !hasReviewed && 
           departureDate.isBefore(DateTime.now());
  }

  // Get status display text in Arabic
  String get statusDisplayText {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'accepted':
        return 'مقبول';
      case 'rejected':
        return 'مرفوض';
      case 'cancelled':
        return 'ملغى';
      default:
        return 'غير معروف';
    }
  }

  // Get status color
  String get statusColor {
    switch (status) {
      case 'pending':
        return 'orange';
      case 'accepted':
        return 'green';
      case 'rejected':
        return 'red';
      case 'cancelled':
        return 'gray';
      default:
        return 'gray';
    }
  }

  @override
  String toString() {
    return 'BookingModel(id: $id, tripId: $tripId, travelerId: $travelerId, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BookingModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
