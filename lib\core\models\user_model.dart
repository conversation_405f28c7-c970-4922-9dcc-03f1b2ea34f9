import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String id;
  final String name; // الاسم
  final String email;
  final String? phoneNumber; // رقم الهاتف
  final String userType; // نوع المستخدم (traveler/leader)
  final String? profileImageUrl; // صورة الملف الشخصي
  final String city; // المدينة
  final String? bio; // نبذة شخصية
  final DateTime createdAt; // تاريخ الإنشاء
  final DateTime updatedAt; // تاريخ التحديث
  final bool isVerified; // مُتحقق منه
  final bool isActive; // نشط
  final int points; // النقاط
  final List<String> badges; // الشارات
  final double rating; // التقييم
  final int totalTrips; // إجمالي الرحلات
  final int completedTrips; // الرحلات المكتملة
  
  // Leader-specific fields
  final String? drivingLicenseUrl; // رخصة القيادة
  final String? idCardUrl; // بطاقة الهوية
  final String? vehicleInfo; // معلومات المركبة
  final String? introVideoUrl; // فيديو التعريف
  final List<String> previousTripImages; // صور الرحلات السابقة
  final bool canOrganizeWomenOnlyTrips; // يمكن تنظيم رحلات نساء فقط
  final bool canOrganizeFamilyTrips; // يمكن تنظيم رحلات عائلية

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.phoneNumber,
    required this.userType,
    this.profileImageUrl,
    required this.city,
    this.bio,
    required this.createdAt,
    required this.updatedAt,
    this.isVerified = false,
    this.isActive = true,
    this.points = 0,
    this.badges = const [],
    this.rating = 0.0,
    this.totalTrips = 0,
    this.completedTrips = 0,
    this.drivingLicenseUrl,
    this.idCardUrl,
    this.vehicleInfo,
    this.introVideoUrl,
    this.previousTripImages = const [],
    this.canOrganizeWomenOnlyTrips = false,
    this.canOrganizeFamilyTrips = false,
  });

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phoneNumber': phoneNumber,
      'userType': userType,
      'profileImageUrl': profileImageUrl,
      'city': city,
      'bio': bio,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isVerified': isVerified,
      'isActive': isActive,
      'points': points,
      'badges': badges,
      'rating': rating,
      'totalTrips': totalTrips,
      'completedTrips': completedTrips,
      'drivingLicenseUrl': drivingLicenseUrl,
      'idCardUrl': idCardUrl,
      'vehicleInfo': vehicleInfo,
      'introVideoUrl': introVideoUrl,
      'previousTripImages': previousTripImages,
      'canOrganizeWomenOnlyTrips': canOrganizeWomenOnlyTrips,
      'canOrganizeFamilyTrips': canOrganizeFamilyTrips,
    };
  }

  // Create from Firestore document
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      phoneNumber: map['phoneNumber'],
      userType: map['userType'] ?? 'traveler',
      profileImageUrl: map['profileImageUrl'],
      city: map['city'] ?? '',
      bio: map['bio'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isVerified: map['isVerified'] ?? false,
      isActive: map['isActive'] ?? true,
      points: map['points'] ?? 0,
      badges: List<String>.from(map['badges'] ?? []),
      rating: (map['rating'] ?? 0.0).toDouble(),
      totalTrips: map['totalTrips'] ?? 0,
      completedTrips: map['completedTrips'] ?? 0,
      drivingLicenseUrl: map['drivingLicenseUrl'],
      idCardUrl: map['idCardUrl'],
      vehicleInfo: map['vehicleInfo'],
      introVideoUrl: map['introVideoUrl'],
      previousTripImages: List<String>.from(map['previousTripImages'] ?? []),
      canOrganizeWomenOnlyTrips: map['canOrganizeWomenOnlyTrips'] ?? false,
      canOrganizeFamilyTrips: map['canOrganizeFamilyTrips'] ?? false,
    );
  }

  // Create from Firestore DocumentSnapshot
  factory UserModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel.fromMap({...data, 'id': doc.id});
  }

  // Copy with method for updates
  UserModel copyWith({
    String? name,
    String? email,
    String? phoneNumber,
    String? userType,
    String? profileImageUrl,
    String? city,
    String? bio,
    DateTime? updatedAt,
    bool? isVerified,
    bool? isActive,
    int? points,
    List<String>? badges,
    double? rating,
    int? totalTrips,
    int? completedTrips,
    String? drivingLicenseUrl,
    String? idCardUrl,
    String? vehicleInfo,
    String? introVideoUrl,
    List<String>? previousTripImages,
    bool? canOrganizeWomenOnlyTrips,
    bool? canOrganizeFamilyTrips,
  }) {
    return UserModel(
      id: id,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      userType: userType ?? this.userType,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      city: city ?? this.city,
      bio: bio ?? this.bio,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      isVerified: isVerified ?? this.isVerified,
      isActive: isActive ?? this.isActive,
      points: points ?? this.points,
      badges: badges ?? this.badges,
      rating: rating ?? this.rating,
      totalTrips: totalTrips ?? this.totalTrips,
      completedTrips: completedTrips ?? this.completedTrips,
      drivingLicenseUrl: drivingLicenseUrl ?? this.drivingLicenseUrl,
      idCardUrl: idCardUrl ?? this.idCardUrl,
      vehicleInfo: vehicleInfo ?? this.vehicleInfo,
      introVideoUrl: introVideoUrl ?? this.introVideoUrl,
      previousTripImages: previousTripImages ?? this.previousTripImages,
      canOrganizeWomenOnlyTrips: canOrganizeWomenOnlyTrips ?? this.canOrganizeWomenOnlyTrips,
      canOrganizeFamilyTrips: canOrganizeFamilyTrips ?? this.canOrganizeFamilyTrips,
    );
  }

  // Check if user is a trip leader
  bool get isLeader => userType == 'leader';

  // Check if user is a traveler
  bool get isTraveler => userType == 'traveler';

  // Get display name
  String get displayName => name.isNotEmpty ? name : email.split('@').first;

  // Check if profile is complete
  bool get isProfileComplete {
    return name.isNotEmpty && 
           city.isNotEmpty && 
           (phoneNumber?.isNotEmpty ?? false);
  }

  // Check if leader verification is complete
  bool get isLeaderVerificationComplete {
    if (!isLeader) return true;
    return drivingLicenseUrl != null && 
           idCardUrl != null && 
           vehicleInfo != null;
  }

  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, userType: $userType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
