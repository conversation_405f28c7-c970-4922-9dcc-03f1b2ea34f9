

class UserModel {
  final String id;
  final String name; // الاسم
  final String email;
  final String? phoneNumber; // رقم الهاتف
  final String userType; // نوع المستخدم (traveler/leader)
  final String? profileImageUrl; // صورة الملف الشخصي
  final String city; // المدينة
  final String? bio; // نبذة شخصية
  final DateTime createdAt; // تاريخ الإنشاء
  final DateTime updatedAt; // تاريخ التحديث
  final bool isVerified; // مُتحقق منه
  final bool isActive; // نشط
  final int points; // النقاط
  final List<String> badges; // الشارات
  final double rating; // التقييم
  final int totalTrips; // إجمالي الرحلات
  final int completedTrips; // الرحلات المكتملة
  
  // Leader-specific fields
  final String? drivingLicenseUrl; // رخصة القيادة
  final String? idCardUrl; // بطاقة الهوية
  final String? vehicleInfo; // معلومات المركبة
  final String? introVideoUrl; // فيديو التعريف
  final List<String> previousTripImages; // صور الرحلات السابقة
  final bool canOrganizeWomenOnlyTrips; // يمكن تنظيم رحلات نساء فقط
  final bool canOrganizeFamilyTrips; // يمكن تنظيم رحلات عائلية

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.phoneNumber,
    required this.userType,
    this.profileImageUrl,
    required this.city,
    this.bio,
    required this.createdAt,
    required this.updatedAt,
    this.isVerified = false,
    this.isActive = true,
    this.points = 0,
    this.badges = const [],
    this.rating = 0.0,
    this.totalTrips = 0,
    this.completedTrips = 0,
    this.drivingLicenseUrl,
    this.idCardUrl,
    this.vehicleInfo,
    this.introVideoUrl,
    this.previousTripImages = const [],
    this.canOrganizeWomenOnlyTrips = false,
    this.canOrganizeFamilyTrips = false,
  });

  // Convert to Map for Supabase
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone_number': phoneNumber,
      'user_type': userType,
      'profile_image_url': profileImageUrl,
      'city': city,
      'bio': bio,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_verified': isVerified,
      'is_active': isActive,
      'points': points,
      'badges': badges,
      'rating': rating,
      'total_trips': totalTrips,
      'completed_trips': completedTrips,
      'driving_license_url': drivingLicenseUrl,
      'id_card_url': idCardUrl,
      'vehicle_info': vehicleInfo,
      'intro_video_url': introVideoUrl,
      'previous_trip_images': previousTripImages,
      'can_organize_women_only_trips': canOrganizeWomenOnlyTrips,
      'can_organize_family_trips': canOrganizeFamilyTrips,
    };
  }

  // Create from Supabase response
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      phoneNumber: map['phone_number'],
      userType: map['user_type'] ?? 'traveler',
      profileImageUrl: map['profile_image_url'],
      city: map['city'] ?? '',
      bio: map['bio'],
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : DateTime.now(),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : DateTime.now(),
      isVerified: map['is_verified'] ?? false,
      isActive: map['is_active'] ?? true,
      points: map['points'] ?? 0,
      badges: List<String>.from(map['badges'] ?? []),
      rating: (map['rating'] ?? 0.0).toDouble(),
      totalTrips: map['total_trips'] ?? 0,
      completedTrips: map['completed_trips'] ?? 0,
      drivingLicenseUrl: map['driving_license_url'],
      idCardUrl: map['id_card_url'],
      vehicleInfo: map['vehicle_info'],
      introVideoUrl: map['intro_video_url'],
      previousTripImages: List<String>.from(map['previous_trip_images'] ?? []),
      canOrganizeWomenOnlyTrips: map['can_organize_women_only_trips'] ?? false,
      canOrganizeFamilyTrips: map['can_organize_family_trips'] ?? false,
    );
  }



  // Copy with method for updates
  UserModel copyWith({
    String? name,
    String? email,
    String? phoneNumber,
    String? userType,
    String? profileImageUrl,
    String? city,
    String? bio,
    DateTime? updatedAt,
    bool? isVerified,
    bool? isActive,
    int? points,
    List<String>? badges,
    double? rating,
    int? totalTrips,
    int? completedTrips,
    String? drivingLicenseUrl,
    String? idCardUrl,
    String? vehicleInfo,
    String? introVideoUrl,
    List<String>? previousTripImages,
    bool? canOrganizeWomenOnlyTrips,
    bool? canOrganizeFamilyTrips,
  }) {
    return UserModel(
      id: id,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      userType: userType ?? this.userType,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      city: city ?? this.city,
      bio: bio ?? this.bio,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      isVerified: isVerified ?? this.isVerified,
      isActive: isActive ?? this.isActive,
      points: points ?? this.points,
      badges: badges ?? this.badges,
      rating: rating ?? this.rating,
      totalTrips: totalTrips ?? this.totalTrips,
      completedTrips: completedTrips ?? this.completedTrips,
      drivingLicenseUrl: drivingLicenseUrl ?? this.drivingLicenseUrl,
      idCardUrl: idCardUrl ?? this.idCardUrl,
      vehicleInfo: vehicleInfo ?? this.vehicleInfo,
      introVideoUrl: introVideoUrl ?? this.introVideoUrl,
      previousTripImages: previousTripImages ?? this.previousTripImages,
      canOrganizeWomenOnlyTrips: canOrganizeWomenOnlyTrips ?? this.canOrganizeWomenOnlyTrips,
      canOrganizeFamilyTrips: canOrganizeFamilyTrips ?? this.canOrganizeFamilyTrips,
    );
  }

  // Check if user is a trip leader
  bool get isLeader => userType == 'leader';

  // Check if user is a traveler
  bool get isTraveler => userType == 'traveler';

  // Get display name
  String get displayName => name.isNotEmpty ? name : email.split('@').first;

  // Check if profile is complete
  bool get isProfileComplete {
    return name.isNotEmpty && 
           city.isNotEmpty && 
           (phoneNumber?.isNotEmpty ?? false);
  }

  // Check if leader verification is complete
  bool get isLeaderVerificationComplete {
    if (!isLeader) return true;
    return drivingLicenseUrl != null && 
           idCardUrl != null && 
           vehicleInfo != null;
  }

  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, userType: $userType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
